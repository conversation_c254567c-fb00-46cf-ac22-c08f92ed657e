from typing import Any, Dict, List

import structlog
from asgiref.sync import sync_to_async
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from temporalio import activity

from didero.integrations.erp.field_mapper import NetSuiteFieldMapper
from didero.integrations.erp.registry import ERPClientRegistry
from didero.integrations.models import ERPIntegrationConfig, ShipmentErpSync
from didero.orders.models import OrderAcknowledgement, PurchaseOrder, Shipment
from didero.tasks.models import Task
from didero.tasks.utils import create_task_v2
from didero.users.models import User
from didero.utils.utils import get_didero_ai_user_email

logger = structlog.get_logger(__name__)


async def get_erp_notification_recipient_guaranteed(shipment: Shipment):
    """
    Get notification recipient with GUARANTEED fallback to Didero AI user

    Priority order:
    1. Purchase order's placed_by user (if available)
    2. Team's default_requestor (if available)
    3. Team's Didero AI user (contractors+{domain}@didero.ai) - GUARANTEED

    Returns:
        User: GUARANTEED to return a User object, never None
    """
    try:
        logger.info("Starting recipient lookup for ERP task", shipment_id=shipment.id)

        purchase_order = shipment.purchase_order
        if not purchase_order:
            logger.info(
                "Shipment has no purchase order, skipping ERP task",
                shipment_id=shipment.id,
            )
            return None

        team = purchase_order.team

        placed_by = purchase_order.placed_by
        if placed_by:
            logger.info(
                "Using placed_by user for ERP task",
                shipment_id=shipment.id,
                user_email=placed_by.email,
            )
            return placed_by

        # Priority 2: Team's default_requestor
        if team and hasattr(team, "default_requestor"):
            default_requestor = team.default_requestor
            if default_requestor:
                logger.info(
                    "Using default_requestor for ERP task",
                    shipment_id=shipment.id,
                    user_email=default_requestor.email,
                )
                return default_requestor

        # Priority 3: Didero AI user
        ai_email = await sync_to_async(get_didero_ai_user_email)(team)
        from didero.users.models.user_models import User

        ai_user, created = await sync_to_async(User.objects.get_or_create)(
            email=ai_email,
            defaults={
                "first_name": "Didero",
                "last_name": "AI",
                "is_active": True,
            },
        )

        if created:
            # Add to team if newly created
            await sync_to_async(ai_user.teams.add)(team)
            logger.info(
                "Created Didero AI user for ERP task",
                shipment_id=shipment.id,
                user_email=ai_email,
            )
        else:
            logger.info(
                "Using existing Didero AI user for ERP task",
                shipment_id=shipment.id,
                user_email=ai_email,
            )

        return ai_user

    except Exception as e:
        logger.exception(
            "CRITICAL: ERP recipient lookup failed",
            shipment_id=shipment.id,
            error=str(e),
        )
        raise Exception(f"ERP recipient lookup failed: {str(e)}")


async def _create_critical_erp_alert(shipment_id: int, error_message: str):
    """
    Create critical system alert when all other notification methods fail.
    This is the absolute last resort to ensure visibility.
    """
    try:
        from django.contrib.contenttypes.models import ContentType

        from didero.orders.models import Shipment
        from didero.tasks.models import Task

        # Use a basic fallback for critical alerts
        from didero.users.models.user_models import User

        system_user, created = await sync_to_async(User.objects.get_or_create)(
            email="<EMAIL>",
            defaults={
                "first_name": "System",
                "last_name": "User",
                "is_active": True,
            },
        )

        # Get content type for shipment
        shipment_content_type = await sync_to_async(ContentType.objects.get_for_model)(
            Shipment
        )

        # Create basic task manually (bypass task_v2 system as last resort)
        await sync_to_async(Task.objects.create)(
            user=system_user,
            content_object_id=str(shipment_id),
            content_type=shipment_content_type,
            summary=f"CRITICAL ERP ALERT: Shipment {shipment_id}",
            description=f"CRITICAL: ERP sync system failure. {error_message}",
            status="pending",
        )

        logger.warning(
            "Created critical ERP system alert",
            shipment_id=shipment_id,
            alert_type="critical_erp_failure",
        )

    except Exception as e:
        # If even this fails, log to critical monitoring
        logger.critical(
            "ABSOLUTE SYSTEM FAILURE: Cannot create critical ERP alert",
            shipment_id=shipment_id,
            error=str(e),
            alert_message=error_message,
        )
        # Could integrate with external monitoring system here (PagerDuty, etc.)


@activity.defn
async def sync_erp_purchase_order(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sync shipment data to configured ERP system.

    This activity follows non-blocking error handling:
    - Success: Returns success status
    - Failure: Returns failure status with error details
    - Never raises exceptions that would block the workflow

    Args:
        params: Contains shipment_id, team_id, and sync_mode

    Returns:
        Dict with:
        - success: bool
        - erp_system: str (e.g., "netsuite")
        - synced_fields: List of fields that were synced
        - error_message: Optional error details
        - requires_task: bool (whether to create a task)
    """
    shipment_id = params["shipment_id"]
    team_id = params["team_id"]
    sync_mode = params.get("sync_mode", "manual")

    logger.info(
        "Starting ERP sync",
        shipment_id=shipment_id,
        team_id=team_id,
        sync_mode=sync_mode,
    )

    try:
        # Load shipment with related data
        logger.info(
            "Loading shipment with preloaded relations", shipment_id=shipment_id
        )
        shipment = await sync_to_async(
            lambda: Shipment.objects.select_related(
                "purchase_order__team",
                "purchase_order__supplier",
                "purchase_order__placed_by",
                "purchase_order__team__default_requestor",  # Preload default_requestor for notifications
            )
            .prefetch_related(
                "purchase_order__order_acknowledgements__items",
                "purchase_order__team__team_domains",  # Preload team domains for AI user fallback
            )
            .get(id=shipment_id)
        )()

        logger.info(
            "Shipment loaded successfully",
            shipment_id=shipment.id,
            po_id=shipment.purchase_order.id,
            team_id=shipment.purchase_order.team.id,
        )

        # Get or create ERP sync record
        erp_sync, created = await sync_to_async(ShipmentErpSync.objects.get_or_create)(
            shipment=shipment,
            defaults={
                "sync_status": "in_progress",
                "attempted_at": timezone.now(),
            },
        )

        if not created:
            # Update existing record
            erp_sync.sync_status = "in_progress"
            erp_sync.attempted_at = timezone.now()
            await sync_to_async(erp_sync.save)(
                update_fields=["sync_status", "attempted_at"]
            )

        # Check if team has ERP integration configured
        try:
            erp_config = await sync_to_async(ERPIntegrationConfig.objects.get)(
                team_id=team_id, enabled=True
            )
        except ERPIntegrationConfig.DoesNotExist:
            erp_sync.sync_status = "not_configured"
            await sync_to_async(erp_sync.save)(update_fields=["sync_status"])

            logger.info(
                "No ERP integration configured for team",
                team_id=team_id,
                shipment_id=shipment_id,
            )

            return {
                "success": True,  # Not an error - just not configured
                "erp_system": "none",
                "synced_fields": [],
                "message": "No ERP integration configured",
                "requires_task": False,
            }

        # Prepare shipment data for sync
        shipment_data = await sync_to_async(NetSuiteFieldMapper.prepare_shipment_data)(
            shipment, erp_config.field_mappings
        )

        # Log shipment data structure for debugging
        logger.info(
            "Prepared shipment data for ERP sync",
            shipment_id=shipment_id,
            po_number=shipment.purchase_order.po_number,
            has_tracking=bool(shipment_data.tracking_number),
            has_line_items=bool(shipment_data.line_items),
            line_items_count=len(shipment_data.line_items)
            if shipment_data.line_items
            else 0,
            estimated_delivery_date=shipment_data.estimated_delivery_date,
            promised_ship_date=shipment_data.promised_ship_date,
        )

        # Determine which fields we're actually syncing WITH VALUES
        fields_to_sync = []
        field_value_pairs = {}
        if shipment_data.tracking_number:
            fields_to_sync.append("tracking_number")
            field_value_pairs["tracking_number"] = shipment_data.tracking_number
        if shipment_data.estimated_delivery_date:
            fields_to_sync.append("estimated_delivery_date")
            field_value_pairs["estimated_delivery_date"] = str(
                shipment_data.estimated_delivery_date
            )
        if shipment_data.promised_ship_date:
            fields_to_sync.append("promised_ship_date")
            field_value_pairs["promised_ship_date"] = str(
                shipment_data.promised_ship_date
            )

        # Log line-item specific data if present
        if shipment_data.line_items:
            line_items = shipment_data.line_items
            logger.info(
                "ERP sync using line-specific data",
                shipment_id=shipment_id,
                po_number=shipment.purchase_order.po_number,
                total_line_items=len(line_items),
                line_details=[
                    {
                        "line": item.line,
                        "item_number": item.item_number,
                        "has_promise_date": bool(item.promised_ship_date),
                        "has_delivery_date": bool(item.estimated_delivery_date),
                    }
                    for item in line_items
                ],
            )

        if not fields_to_sync:
            logger.warning(
                "No fields available to sync",
                shipment_id=shipment_id,
                po_number=shipment.purchase_order.po_number,
            )

            erp_sync.sync_status = "success"
            erp_sync.completed_at = timezone.now()
            erp_sync.synced_fields = []
            await sync_to_async(erp_sync.save)(
                update_fields=["sync_status", "completed_at", "synced_fields"]
            )

            return {
                "success": True,
                "erp_system": erp_config.erp_type,
                "synced_fields": [],
                "message": "No fields to sync",
                "requires_task": False,
            }

        # Get ERP client
        from didero.integrations.erp.credentials import SimpleEnvCredentialProvider

        # Get credentials
        credential_provider = SimpleEnvCredentialProvider()
        credentials = credential_provider.get_credentials(
            str(team_id), erp_config.erp_type
        )

        client = ERPClientRegistry.create_client(
            erp_type=erp_config.erp_type,
            credentials=credentials,
            config=erp_config.config,
            field_mappings=erp_config.field_mappings,
        )

        # Execute sync with enhanced logging
        sync_start_time = timezone.now()
        logger.info(
            "Syncing shipment to ERP - STARTING",
            shipment_id=shipment_id,
            po_number=shipment.purchase_order.po_number,
            erp_system=erp_config.erp_type,
            fields_to_sync=fields_to_sync,
            shipment_data=shipment_data,  # Log actual data being sent
            sync_mode=sync_mode,
            sync_start_time=sync_start_time.isoformat(),
        )

        try:
            result = client.update_shipment(
                po_number=shipment.purchase_order.po_number, shipment_data=shipment_data
            )
            sync_duration = (timezone.now() - sync_start_time).total_seconds()

            logger.info(
                "ERP sync request completed",
                shipment_id=shipment_id,
                po_number=shipment.purchase_order.po_number,
                sync_success=result.success,
                sync_duration_seconds=sync_duration,
                result_error=result.error_message if not result.success else None,
            )
        except Exception as sync_error:
            sync_duration = (timezone.now() - sync_start_time).total_seconds()
            logger.error(
                "ERP sync request exception",
                shipment_id=shipment_id,
                po_number=shipment.purchase_order.po_number,
                sync_duration_seconds=sync_duration,
                sync_error=str(sync_error),
                sync_error_type=type(sync_error).__name__,
            )
            # Re-create ERPSyncResult for consistent error handling
            from didero.integrations.erp.schemas import ERPSyncResult

            result = ERPSyncResult(success=False, error_message=str(sync_error))

        if result.success:
            # Update shipment status
            erp_sync.sync_status = "success"
            erp_sync.completed_at = timezone.now()
            erp_sync.error = ""
            erp_sync.synced_fields = fields_to_sync
            erp_sync.erp_type = erp_config.erp_type
            await sync_to_async(erp_sync.save)(
                update_fields=[
                    "sync_status",
                    "completed_at",
                    "error",
                    "synced_fields",
                    "erp_type",
                ]
            )

            logger.info(
                "ERP sync successful",
                shipment_id=shipment_id,
                po_number=shipment.purchase_order.po_number,
                synced_fields=fields_to_sync,
            )

            # GUARANTEED success task creation for visibility and audit trail
            logger.info(
                "Creating GUARANTEED success task",
                shipment_id=shipment_id,
                po_number=shipment.purchase_order.po_number,
                sync_mode=sync_mode,
                synced_fields=fields_to_sync,
            )

            await create_erp_sync_success_task(
                shipment=shipment,
                synced_fields=fields_to_sync,
                field_value_pairs=field_value_pairs,
                erp_system=erp_config.erp_type,
                sync_mode=sync_mode,
            )

            return {
                "success": True,
                "erp_system": erp_config.erp_type,
                "synced_fields": fields_to_sync,
                "requires_task": True,  # Success task was created
            }

        else:
            # Sync failed - update status and prepare error details
            error_message = result.error_message or "Unknown error"

            erp_sync.sync_status = "failed"
            erp_sync.error = error_message
            erp_sync.erp_type = erp_config.erp_type
            await sync_to_async(erp_sync.save)(
                update_fields=["sync_status", "error", "erp_type"]
            )

            logger.error(
                "ERP sync failed",
                shipment_id=shipment_id,
                po_number=shipment.purchase_order.po_number,
                error=error_message,
            )

            # GUARANTEED error task creation for visibility
            logger.info(
                "Creating GUARANTEED error task",
                shipment_id=shipment_id,
                po_number=shipment.purchase_order.po_number,
                error_message=error_message,
            )

            await create_erp_sync_error_task(
                shipment=shipment,
                error_message=error_message,
                fields_to_sync=fields_to_sync,
                erp_system=erp_config.erp_type,
            )

            return {
                "success": False,
                "erp_system": erp_config.erp_type,
                "synced_fields": [],
                "error_message": error_message,
                "requires_task": True,  # Error task was created
            }

    except Exception as e:
        logger.exception(
            "Unexpected error in ERP sync", shipment_id=shipment_id, error=str(e)
        )

        # Try to update shipment status if possible
        try:
            logger.info(
                "Exception handler - reloading shipment with relations",
                shipment_id=shipment_id,
            )
            shipment = await sync_to_async(
                lambda: Shipment.objects.select_related(
                    "purchase_order__team",
                    "purchase_order__supplier",
                    "purchase_order__placed_by",
                    "purchase_order__team__default_requestor",  # Preload default_requestor for notifications
                )
                .prefetch_related(
                    "purchase_order__team__team_domains",  # Preload team domains for AI user fallback
                )
                .get(id=shipment_id)
            )()

            logger.info(
                "Exception handler - shipment reloaded", shipment_id=shipment.id
            )

            # Get or create ERP sync record for exception handling
            erp_sync, created = await sync_to_async(
                ShipmentErpSync.objects.get_or_create
            )(
                shipment=shipment,
                defaults={
                    "sync_status": "failed",
                    "error": f"Unexpected error: {str(e)}",
                    "attempted_at": timezone.now(),
                },
            )

            if not created:
                erp_sync.sync_status = "failed"
                erp_sync.error = f"Unexpected error: {str(e)}"
                await sync_to_async(erp_sync.save)(
                    update_fields=["sync_status", "error"]
                )

            logger.info(
                "Exception handler - creating GUARANTEED error task",
                shipment_id=shipment.id,
            )
            # GUARANTEED error task creation even in exception scenarios
            await create_erp_sync_error_task(
                shipment=shipment,
                error_message=str(e),
                fields_to_sync=["unknown"],
                erp_system="unknown",
            )
            logger.info(
                "Exception handler - GUARANTEED error task created successfully",
                shipment_id=shipment.id,
            )
        except Exception as shipment_error:
            # Even if shipment update fails, we still need error visibility
            logger.exception(
                "Shipment update failed in exception handler, creating critical alert",
                shipment_id=shipment_id,
                original_error=str(e),
                shipment_error=str(shipment_error),
            )

            # ABSOLUTE LAST RESORT: Create system-level critical alert
            try:
                await _create_critical_erp_alert(
                    shipment_id=shipment_id,
                    error_message=f"ERP sync failed: {str(e)} AND shipment update failed: {str(shipment_error)}",
                )
            except Exception as alert_error:
                logger.critical(
                    "COMPLETE SYSTEM FAILURE: Cannot create any ERP notification",
                    shipment_id=shipment_id,
                    original_error=str(e),
                    shipment_error=str(shipment_error),
                    alert_error=str(alert_error),
                )

        return {
            "success": False,
            "erp_system": "unknown",
            "synced_fields": [],
            "error_message": str(e),
            "requires_task": True,  # We ALWAYS attempt task creation
            "task_guarantee_attempted": True,
        }


async def create_erp_sync_success_task(
    shipment: Shipment,
    synced_fields: list,
    field_value_pairs: dict,
    erp_system: str,
    sync_mode: str,
) -> None:
    """
    Create success notification task for ERP sync completion.

    This function GUARANTEES task creation - it will never silently fail.
    Uses the same Didero AI pattern as human validation tasks.
    """
    try:
        # GUARANTEED recipient using proven Didero AI pattern
        recipient = await get_erp_notification_recipient_guaranteed(shipment)

        logger.info(
            "Creating ERP sync SUCCESS task",
            shipment_id=shipment.id,
            recipient_email=recipient.email,
            sync_mode=sync_mode,
        )

        # Extract data we need (avoid ForeignKey access in async context)
        po_number = shipment.purchase_order.po_number
        po_id = shipment.purchase_order.id

        # Format fields for display with VALUES
        fields_display = ", ".join(synced_fields) if synced_fields else "none"
        field_values_display = (
            "; ".join(
                [f"{field}: {value}" for field, value in field_value_pairs.items()]
            )
            if field_value_pairs
            else "none"
        )

        # Determine success message based on sync mode
        if sync_mode == "automatic":
            sync_type = "Automatic ERP sync completed"
        else:
            sync_type = "Manual ERP sync completed"

        # Create success task using new task type
        from django.contrib.contenttypes.models import ContentType

        po_content_type = await sync_to_async(ContentType.objects.get_for_model)(
            shipment.purchase_order
        )

        await sync_to_async(create_task_v2)(
            task_type="ERP_SYNC_SUCCESS",
            user=recipient,
            model_type=po_content_type,
            model_id=str(po_id),
            task_type_params={
                "po_number": po_number,
                "sync_mode": sync_mode.title(),
                "synced_fields": fields_display,
                "field_values": field_values_display,
                "erp_system": erp_system.upper(),
                "success_message": sync_type,
            },
        )

        logger.info(
            "SUCCESS: Created ERP sync success task",
            shipment_id=shipment.id,
            po_number=shipment.purchase_order.po_number,
            assigned_to=recipient.email,
            sync_mode=sync_mode,
            task_guaranteed=True,
        )

    except Exception as e:
        # CRITICAL: This should never happen with guaranteed recipient
        logger.exception(
            "CRITICAL: ERP sync success task creation failed despite guaranteed recipient",
            shipment_id=shipment.id,
            error=str(e),
        )
        # Re-raise to ensure visibility - this indicates a serious system issue
        raise Exception(f"ERP success task creation failed: {str(e)}")


async def create_erp_sync_error_task(
    shipment: Shipment, error_message: str, fields_to_sync: list, erp_system: str
) -> None:
    """
    Create error notification task for ERP sync failure.

    This function GUARANTEES task creation - it will never silently fail.
    Uses the same Didero AI pattern as human validation tasks.
    """
    try:
        # GUARANTEED recipient using proven Didero AI pattern
        recipient = await get_erp_notification_recipient_guaranteed(shipment)

        logger.info(
            "Creating ERP sync ERROR task",
            shipment_id=shipment.id,
            recipient_email=recipient.email,
            error_message=error_message,
        )

        # Extract data we need (avoid ForeignKey access in async context)
        po_number = shipment.purchase_order.po_number
        po_id = shipment.purchase_order.id

        # Format fields for display
        fields_display = ", ".join(fields_to_sync) if fields_to_sync else "none"

        # Create error task using existing pattern
        from django.contrib.contenttypes.models import ContentType

        po_content_type = await sync_to_async(ContentType.objects.get_for_model)(
            shipment.purchase_order
        )
        await sync_to_async(create_task_v2)(
            task_type="ERP_SYNC_ERROR",
            user=recipient,
            model_type=po_content_type,
            model_id=str(po_id),
            task_type_params={
                "po_number": po_number,
                "error_message": error_message,
                "sync_fields": fields_display,
                "erp_system": erp_system.upper(),
            },
        )
        logger.info(
            "SUCCESS: Created ERP sync error task",
            shipment_id=shipment.id,
            po_number=shipment.purchase_order.po_number,
            assigned_to=recipient.email,
            task_guaranteed=True,
        )

    except Exception as e:
        # CRITICAL: This should never happen with guaranteed recipient
        logger.exception(
            "CRITICAL: ERP sync error task creation failed despite guaranteed recipient",
            shipment_id=shipment.id,
            error=str(e),
        )
        # Re-raise to ensure visibility - this indicates a serious system issue
        raise Exception(f"ERP error task creation failed: {str(e)}")


async def get_erp_notification_recipient_for_po(purchase_order: PurchaseOrder):
    """
    Get notification recipient for PO-based ERP operations (OA sync).
    Mirrors the logic from get_erp_notification_recipient_guaranteed but for POs.

    Priority order:
    1. Purchase order's placed_by user (if available)
    2. Team's default_requestor (if available)
    3. Team's Didero AI user (contractors+{domain}@didero.ai) - GUARANTEED
    """
    try:
        logger.info(
            "Starting recipient lookup for PO ERP task", po_id=purchase_order.id
        )

        team = purchase_order.team

        # Priority 1: PO's placed_by user
        if purchase_order.placed_by:
            logger.info(
                "Using placed_by user for ERP task",
                po_id=purchase_order.id,
                user_email=purchase_order.placed_by.email,
            )
            return purchase_order.placed_by

        # Priority 2: Team's default_requestor
        if team and hasattr(team, "default_requestor") and team.default_requestor:
            logger.info(
                "Using default_requestor for ERP task",
                po_id=purchase_order.id,
                user_email=team.default_requestor.email,
            )
            return team.default_requestor

        # Priority 3: Didero AI user (GUARANTEED)
        ai_email = await sync_to_async(get_didero_ai_user_email)(team)
        from didero.users.models.user_models import User

        ai_user, created = await sync_to_async(User.objects.get_or_create)(
            email=ai_email,
            defaults={
                "first_name": "Didero",
                "last_name": "AI",
                "is_active": True,
            },
        )

        if created:
            await sync_to_async(ai_user.teams.add)(team)
            logger.info(
                "Created Didero AI user for ERP task",
                po_id=purchase_order.id,
                user_email=ai_email,
            )
        else:
            logger.info(
                "Using existing Didero AI user for ERP task",
                po_id=purchase_order.id,
                user_email=ai_email,
            )

        return ai_user

    except Exception as e:
        logger.exception(
            "CRITICAL: PO ERP recipient lookup failed",
            po_id=purchase_order.id,
            error=str(e),
        )
        raise Exception(f"PO ERP recipient lookup failed: {str(e)}")


@activity.defn
async def sync_erp_order_acknowledgement(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sync OA promised delivery dates to NetSuite expectedreceiptdate field.

    Args:
        params: {
            "oa_id": int,
            "po_id": int,
            "team_id": int,
            "sync_mode": "automatic" | "manual"
        }

    Returns:
        Dict with success status, synced fields, and task creation info
    """
    oa_id = params["oa_id"]
    po_id = params["po_id"]
    team_id = params["team_id"]
    sync_mode = params.get("sync_mode", "manual")

    logger.info(
        "Starting OA ERP sync for date changes",
        oa_id=oa_id,
        po_id=po_id,
        team_id=team_id,
        sync_mode=sync_mode,
    )

    try:
        # Load OA with all related data
        oa = await sync_to_async(
            lambda: OrderAcknowledgement.objects.select_related(
                "purchase_order__team",
                "purchase_order__supplier",
                "purchase_order__placed_by",
                "purchase_order__team__default_requestor",
            )
            .prefetch_related(
                "items__item",
                "purchase_order__team__team_domains",
            )
            .get(id=oa_id)
        )()

        po = oa.purchase_order

        # Verify PO has requested_date
        if not po.requested_date:
            logger.warning(
                "PO has no requested_date, skipping OA ERP sync",
                po_number=po.po_number,
                oa_id=oa_id,
            )
            return {
                "success": True,
                "erp_system": "none",
                "synced_fields": [],
                "message": "PO has no requested date for comparison",
                "requires_task": False,
            }

        # Check ERP configuration
        try:
            erp_config = await sync_to_async(ERPIntegrationConfig.objects.get)(
                team_id=team_id, enabled=True
            )
        except ERPIntegrationConfig.DoesNotExist:
            logger.info(
                "No ERP integration configured for team",
                team_id=team_id,
                oa_id=oa_id,
            )
            return {
                "success": True,
                "erp_system": "none",
                "synced_fields": [],
                "message": "No ERP integration configured",
                "requires_task": False,
            }

        # Prepare OA data checking for date differences
        oa_data = await sync_to_async(NetSuiteFieldMapper.prepare_oa_data)(
            oa, po, erp_config.field_mappings
        )

        # No changes to sync
        if not oa_data.line_items:
            logger.info(
                "No OA date changes to sync",
                oa_id=oa_id,
                po_number=po.po_number,
            )
            return {
                "success": True,
                "erp_system": erp_config.erp_type,
                "synced_fields": [],
                "message": "No date differences detected",
                "requires_task": False,
            }

        # Build change summary for logging and tasks
        changed_lines = []
        date_changes_summary = []

        for item in oa_data.line_items:
            change_info = {
                "line": item.line,
                "item_number": item.item_number,
                "old_date": str(po.requested_date),
                "new_date": str(item.estimated_delivery_date),
            }
            changed_lines.append(change_info)
            date_changes_summary.append(
                f"Line {item.line} ({item.item_number}): "
                f"{po.requested_date} → {item.estimated_delivery_date}"
            )

        logger.info(
            "Syncing OA date changes to NetSuite",
            oa_id=oa_id,
            po_number=po.po_number,
            erp_system=erp_config.erp_type,
            changed_lines=changed_lines,
        )

        # Get credentials and create client
        from didero.integrations.erp.credentials import SimpleEnvCredentialProvider

        credential_provider = SimpleEnvCredentialProvider()
        credentials = credential_provider.get_credentials(
            str(team_id), erp_config.erp_type
        )

        client = ERPClientRegistry.create_client(
            erp_type=erp_config.erp_type,
            credentials=credentials,
            config=erp_config.config,
            field_mappings=erp_config.field_mappings,
        )

        # Execute sync
        sync_start_time = timezone.now()
        try:
            result = client.update_purchase_order(po.po_number, oa_data)
            sync_duration = (timezone.now() - sync_start_time).total_seconds()

            logger.info(
                "OA ERP sync completed",
                oa_id=oa_id,
                po_number=po.po_number,
                sync_success=result.success,
                sync_duration_seconds=sync_duration,
                lines_updated=len(changed_lines),
            )
        except Exception as sync_error:
            sync_duration = (timezone.now() - sync_start_time).total_seconds()
            logger.error(
                "OA ERP sync failed",
                oa_id=oa_id,
                po_number=po.po_number,
                sync_duration_seconds=sync_duration,
                sync_error=str(sync_error),
            )
            from didero.integrations.erp.schemas import ERPSyncResult

            result = ERPSyncResult(success=False, error_message=str(sync_error))

        # Handle results
        if result.success:
            # Create success task
            await create_oa_erp_sync_success_task(
                order_acknowledgement=oa,
                changed_lines=changed_lines,
                date_changes_summary="; ".join(date_changes_summary),
                erp_system=erp_config.erp_type,
                sync_mode=sync_mode,
            )

            return {
                "success": True,
                "erp_system": erp_config.erp_type,
                "synced_lines": changed_lines,
                "requires_task": True,
            }
        else:
            # Create error task
            await create_oa_erp_sync_error_task(
                order_acknowledgement=oa,
                error_message=result.error_message or "Unknown error",
                attempted_changes="; ".join(date_changes_summary),
                erp_system=erp_config.erp_type,
            )

            return {
                "success": False,
                "erp_system": erp_config.erp_type,
                "error_message": result.error_message,
                "requires_task": True,
            }

    except Exception as e:
        logger.exception(
            "Unexpected error in OA ERP sync",
            oa_id=oa_id,
            error=str(e),
        )

        # Try to create error task with available info
        try:
            await _create_critical_erp_alert(oa_id, f"OA ERP sync failed: {str(e)}")
        except:
            pass

        return {
            "success": False,
            "erp_system": "unknown",
            "error_message": str(e),
            "requires_task": True,
        }


async def create_oa_erp_sync_success_task(
    order_acknowledgement: OrderAcknowledgement,
    changed_lines: List[Dict[str, Any]],
    date_changes_summary: str,
    erp_system: str,
    sync_mode: str,
) -> None:
    """Create success notification task for OA ERP sync completion."""
    try:
        recipient = await get_erp_notification_recipient_for_po(
            order_acknowledgement.purchase_order
        )

        po = order_acknowledgement.purchase_order
        oa_number = (
            order_acknowledgement.order_number or f"OA-{order_acknowledgement.id}"
        )

        from django.contrib.contenttypes.models import ContentType

        po_content_type = await sync_to_async(ContentType.objects.get_for_model)(po)

        await sync_to_async(create_task_v2)(
            task_type="OA_ERP_SYNC_SUCCESS",
            user=recipient,
            model_type=po_content_type,
            model_id=str(po.id),
            task_type_params={
                "po_number": po.po_number,
                "oa_number": oa_number,
                "sync_mode": sync_mode.title(),
                "updated_lines": f"{len(changed_lines)} line(s)",
                "date_changes": date_changes_summary,
                "erp_system": erp_system.upper(),
            },
        )

        logger.info(
            "Created OA ERP sync success task",
            oa_id=order_acknowledgement.id,
            po_number=po.po_number,
            assigned_to=recipient.email,
        )

    except Exception as e:
        logger.exception(
            "Failed to create OA ERP sync success task",
            oa_id=order_acknowledgement.id,
            error=str(e),
        )
        raise


async def create_oa_erp_sync_error_task(
    order_acknowledgement: OrderAcknowledgement,
    error_message: str,
    attempted_changes: str,
    erp_system: str,
) -> None:
    """Create error notification task for OA ERP sync failure."""
    try:
        recipient = await get_erp_notification_recipient_for_po(
            order_acknowledgement.purchase_order
        )

        po = order_acknowledgement.purchase_order
        oa_number = (
            order_acknowledgement.order_number or f"OA-{order_acknowledgement.id}"
        )

        from django.contrib.contenttypes.models import ContentType

        po_content_type = await sync_to_async(ContentType.objects.get_for_model)(po)

        await sync_to_async(create_task_v2)(
            task_type="OA_ERP_SYNC_ERROR",
            user=recipient,
            model_type=po_content_type,
            model_id=str(po.id),
            task_type_params={
                "po_number": po.po_number,
                "oa_number": oa_number,
                "error_message": error_message,
                "attempted_changes": attempted_changes,
                "erp_system": erp_system.upper(),
            },
        )

        logger.info(
            "Created OA ERP sync error task",
            oa_id=order_acknowledgement.id,
            po_number=po.po_number,
            assigned_to=recipient.email,
        )

    except Exception as e:
        logger.exception(
            "Failed to create OA ERP sync error task",
            oa_id=order_acknowledgement.id,
            error=str(e),
        )
        raise
