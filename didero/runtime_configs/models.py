from enum import StrEnum
from typing import Any

from auditlog.registry import auditlog
from django.db import models

from didero import cache_util
from didero.models import BaseModel

RUNTIME_CONFIG_CACHE_PREFIX = "RUNTIME_CONFIG_"


class RuntimeConfigEnum(StrEnum):
    TASKS_EMAIL_SERVICE_DISABLED = "TASKS_EMAIL_SERVICE_DISABLED"
    POST_AI_PURCHASE_ORDER_STATUS_UPDATES_TO_SLACK = (
        "POST_AI_PURCHASE_ORDER_STATUS_UPDATES_TO_SLACK"
    )
    POST_COMMENTS_ON_DIDERO_TO_SLACK = "POST_COMMENTS_ON_DIDERO_TO_SLACK"
    DEMO_GENERATION_DELAY_AFTER_ASYNC_TASKS = "DEMO_GENERATION_DELAY_AFTER_ASYNC_TASKS"
    PO_EXTRACTION_AI_MODEL = "PO_EXTRACTION_AI_MODEL"
    USE_EMAIL_CONSUMER_V2 = "USE_EMAIL_CONSUMER_V2"


class RuntimeConfig(BaseModel):
    key = models.CharField(unique=True)
    value = models.JSONField()

    class Meta:
        indexes = [
            models.Index(fields=["key"]),
        ]

    def __str__(self):
        return f"{self.key}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # break/update the cache on successful saves/updates
        cache_util.save(
            key=f"{RUNTIME_CONFIG_CACHE_PREFIX}{self.key}", value=self.value
        )

    @classmethod
    def get_or_default(cls, key: RuntimeConfigEnum, default: Any = None):
        # check the cache to see if the user-email pair exists
        cached_value = cache_util.get(key=f"{RUNTIME_CONFIG_CACHE_PREFIX}{key}")
        if cached_value is not None:
            return cached_value
        else:
            runtime_config = RuntimeConfig.objects.filter(key=key).first()
            value = runtime_config.value if runtime_config else default

            # cache and return
            cache_util.save(key=f"{RUNTIME_CONFIG_CACHE_PREFIX}{key}", value=value)
            return value

    @classmethod
    def get_tasks_email_service_disabled(cls) -> bool:
        return RuntimeConfig.get_or_default(
            RuntimeConfigEnum.TASKS_EMAIL_SERVICE_DISABLED, False
        )


auditlog.register(RuntimeConfig)
