"""
Tests for <PERSON><PERSON>s email notification Celery tasks.
"""

import json
from unittest.mock import MagicMock, patch

from django.test import TestCase

from didero.emails.models import EmailCredentialNylas
from didero.emails.schemas import NylasGrantStatus
from didero.emails.tasks.nylas_notifications import process_nylas_email_notification
from didero.suppliers.models import Supplier, SupplierDomain
from didero.users.tests.factories import TeamFactory, UserFactory


class TestNylasNotifications(TestCase):
    def setUp(self):
        self.team = TeamFactory()
        self.user = UserFactory(team=self.team)
        self.supplier = Supplier.objects.create(
            name="Test Supplier",
            team=self.team,
        )
        self.supplier_domain = SupplierDomain.objects.create(
            domain="supplier.com",
            supplier=self.supplier,
            team=self.team,
        )
        self.credential = EmailCredentialNylas.objects.create(
            team=self.team,
            grant_id="test-grant-123",
            email_address="<EMAIL>",
            status=NylasGrantStatus.VALID,
        )

    def test_process_nylas_email_notification_with_sns_envelope(self):
        """Test processing a <PERSON>ylas notification wrapped in SNS envelope."""
        nylas_message = {
            "id": "msg-123",
            "type": "message.created",
            "data": {
                "object": {
                    "id": "msg-123",
                    "grant_id": "test-grant-123",
                    "from": [{"email": "<EMAIL>", "name": "Sender"}],
                    "to": [{"email": "<EMAIL>", "name": "Team"}],
                    "cc": [],
                    "bcc": [],
                    "reply_to": [],
                    "subject": "Test Email",
                    "body": "Test email body",
                    "attachments": [],
                    "thread_id": "thread-123",
                    "date": 1234567890,
                    "folders": ["inbox"],
                }
            },
        }

        sns_message = {
            "Message": json.dumps(nylas_message),
            "MessageId": "sns-msg-123",
            "TopicArn": "arn:aws:sns:us-east-1:123456789012:nylas-webhooks",
        }

        with patch(
            "didero.emails.tasks.nylas_tasks.create_communication_and_documents"
        ) as mock_create_comm:
            process_nylas_email_notification(sns_message)

            # Verify the communication was created
            mock_create_comm.assert_called_once()
            call_args = mock_create_comm.call_args
            self.assertEqual(call_args[0][0], self.credential)
            self.assertEqual(call_args[0][1], self.supplier)
            self.assertEqual(call_args[0][2]["id"], "msg-123")

    def test_process_nylas_email_notification_outgoing_email(self):
        """Test processing an outgoing email notification via SNS."""
        nylas_message = {
            "id": "msg-456",
            "type": "message.created",
            "data": {
                "object": {
                    "id": "msg-456",
                    "grant_id": "test-grant-123",
                    "from": [{"email": "<EMAIL>", "name": "Team"}],
                    "to": [{"email": "<EMAIL>", "name": "Recipient"}],
                    "cc": [],
                    "bcc": [],
                    "reply_to": [],
                    "subject": "Outgoing Email",
                    "body": "Outgoing email body",
                    "attachments": [],
                    "thread_id": "thread-456",
                    "date": 1234567890,
                    "folders": ["sent"],
                }
            },
        }

        sns_message = {
            "Message": json.dumps(nylas_message),
            "MessageId": "sns-msg-456",
            "TopicArn": "arn:aws:sns:us-east-1:123456789012:nylas-webhooks",
        }

        with patch(
            "didero.emails.tasks.nylas_tasks.create_communication_and_documents"
        ) as mock_create_comm:
            process_nylas_email_notification(sns_message)

            # Verify the communication was created
            mock_create_comm.assert_called_once()
            call_args = mock_create_comm.call_args
            self.assertEqual(call_args[0][0], self.credential)
            self.assertEqual(call_args[0][1], self.supplier)
            self.assertEqual(call_args[0][2]["id"], "msg-456")

    def test_process_grant_expired_notification(self):
        """Test processing a grant expired notification."""
        nylas_message = {
            "id": "event-789",
            "type": "grant.expired",
            "data": {
                "object": {
                    "grant_id": "test-grant-123",
                }
            },
        }

        sns_message = {
            "Message": json.dumps(nylas_message),
            "MessageId": "sns-msg-789",
            "TopicArn": "arn:aws:sns:us-east-1:123456789012:nylas-webhooks",
        }

        # Verify credential is initially valid
        self.assertEqual(self.credential.status, NylasGrantStatus.VALID)

        process_nylas_email_notification(sns_message)

        # Refresh from database
        self.credential.refresh_from_db()

        # Verify credential is now invalid
        self.assertEqual(self.credential.status, NylasGrantStatus.INVALID)

    def test_process_unknown_event_type(self):
        """Test processing an unknown event type logs warning but doesn't fail."""
        nylas_message = {
            "id": "event-unknown",
            "type": "unknown.event",
            "data": {"object": {}},
        }

        sns_message = {
            "Message": json.dumps(nylas_message),
            "MessageId": "sns-msg-unknown",
            "TopicArn": "arn:aws:sns:us-east-1:123456789012:nylas-webhooks",
        }

        with patch("didero.emails.tasks.nylas_notifications.log") as mock_log:
            # Should not raise exception
            process_nylas_email_notification(sns_message)

            # Verify warning was logged
            mock_log.warning.assert_called_once()
            call_args = mock_log.warning.call_args
            self.assertIn("unknown event type", call_args[0][0])

    def test_process_email_no_matching_supplier(self):
        """Test processing email with no matching supplier domain."""
        nylas_message = {
            "id": "msg-no-match",
            "type": "message.created",
            "data": {
                "object": {
                    "id": "msg-no-match",
                    "grant_id": "test-grant-123",
                    "from": [{"email": "<EMAIL>", "name": "Unknown"}],
                    "to": [{"email": "<EMAIL>", "name": "Team"}],
                    "cc": [],
                    "bcc": [],
                    "reply_to": [],
                    "subject": "Unknown Email",
                    "body": "Email from unknown domain",
                    "attachments": [],
                    "thread_id": "thread-unknown",
                    "date": 1234567890,
                    "folders": ["inbox"],
                }
            },
        }

        sns_message = {
            "Message": json.dumps(nylas_message),
            "MessageId": "sns-msg-no-match",
            "TopicArn": "arn:aws:sns:us-east-1:123456789012:nylas-webhooks",
        }

        with patch(
            "didero.emails.tasks.nylas_tasks.create_communication_and_documents"
        ) as mock_create_comm:
            process_nylas_email_notification(sns_message)

            # Verify no communication was created
            mock_create_comm.assert_not_called()

    def test_retry_on_exception(self):
        """Test that the task retries on exceptions."""
        nylas_message = {
            "id": "msg-error",
            "type": "message.created",
            "data": {
                "object": {
                    "id": "msg-error",
                    "grant_id": "test-grant-123",
                    "from": [{"email": "<EMAIL>", "name": "Sender"}],
                    "to": [{"email": "<EMAIL>", "name": "Team"}],
                    "cc": [],
                    "bcc": [],
                    "reply_to": [],
                    "subject": "Error Email",
                    "body": "This will cause an error",
                    "attachments": [],
                    "thread_id": "thread-error",
                    "date": 1234567890,
                    "folders": ["inbox"],
                }
            },
        }

        sns_message = {
            "Message": json.dumps(nylas_message),
            "MessageId": "sns-msg-error",
            "TopicArn": "arn:aws:sns:us-east-1:123456789012:nylas-webhooks",
        }

        # Mock the task to track retries
        mock_task = MagicMock()
        mock_task.request.id = "task-123"

        with patch(
            "didero.emails.tasks.nylas_tasks.create_communication_and_documents"
        ) as mock_create_comm:
            mock_create_comm.side_effect = Exception("Database error")

            # Call the task function directly with self parameter
            with self.assertRaises(Exception):
                process_nylas_email_notification.run(mock_task, sns_message)

    def test_malformed_sns_message(self):
        """Test handling of malformed SNS messages."""
        # Test missing Message field
        sns_message_no_message = {
            "MessageId": "sns-msg-bad",
            "TopicArn": "arn:aws:sns:us-east-1:123456789012:nylas-webhooks",
        }

        with patch("didero.emails.tasks.nylas_notifications.log") as mock_log:
            process_nylas_email_notification(sns_message_no_message)
            mock_log.error.assert_called_with(
                "SNS message missing 'Message' field",
                sns_message=sns_message_no_message,
            )

        # Test invalid JSON in Message field
        sns_message_bad_json = {
            "Message": "not-valid-json",
            "MessageId": "sns-msg-bad-json",
            "TopicArn": "arn:aws:sns:us-east-1:123456789012:nylas-webhooks",
        }

        with patch("didero.emails.tasks.nylas_notifications.log") as mock_log:
            process_nylas_email_notification(sns_message_bad_json)
            mock_log.error.assert_called_with(
                "Could not decode SNS message", sns_message=sns_message_bad_json
            )
