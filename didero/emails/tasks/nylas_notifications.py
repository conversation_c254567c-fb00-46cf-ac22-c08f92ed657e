"""
Celery tasks for processing <PERSON><PERSON>s email notifications from SNS/SQS.

This module replaces the standalone email consumer process with Celery-based
processing of <PERSON><PERSON><PERSON> webhook events.
"""

import json
import time
from typing import List, Optional

import structlog
from celery import shared_task

from didero.emails.models import EmailCredentialNylas
from didero.emails.schemas import (
    EmailParticipants,
    NylasEmailMessage,
    NylasGrantExpiredDict,
    NylasGrantStatus,
    NylasWebhookPayload,
    SupplierLookupData,
)
from didero.emails.tasks.nylas_tasks import create_communication_and_documents
from didero.runtime_configs.models import RuntimeConfig, RuntimeConfigEnum
from didero.suppliers.models import (
    Communication,
    Supplier,
    SupplierContact,
    SupplierDomain,
)
from didero.users.models import Team

log = structlog.get_logger(__name__)


@shared_task(
    queue="nylas_email_notifications",
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
)
def process_nylas_notification(self, message_body: str):
    """
    Process Nylas email notifications from SQS.

    This task handles raw Nylas webhook payloads:
    - message.created events (new emails)
    - grant.expired events (credential expiration)

    Args:
        message_body: The raw Nylas webhook JSON as a string
    """
    # Check if v2 email consumer is enabled
    use_v2_consumer = RuntimeConfig.get_or_default(
        RuntimeConfigEnum.USE_EMAIL_CONSUMER_V2, False
    )

    if not use_v2_consumer:
        log.info(
            "Email consumer v2 is disabled via RuntimeConfig, skipping SQS processing",
            task_id=self.request.id,
        )
        return

    # Parse the webhook payload directly
    try:
        webhook_payload: NylasWebhookPayload = json.loads(message_body)
    except json.JSONDecodeError:
        log.error("Could not decode webhook payload", message_body=message_body[:500])
        return

    # Process the webhook
    try:
        message_id = webhook_payload.get("id")
        msg_type = webhook_payload.get("type")
        data_obj = webhook_payload.get("data", {}).get("object", {})

        log.info(
            "Processing Nylas webhook event",
            message_id=message_id,
            event_type=msg_type,
            task_id=self.request.id,
        )

        if msg_type == "message.created":
            # Convert dict to Pydantic model
            try:
                email_message = NylasEmailMessage(**data_obj)
            except Exception as e:
                log.error(
                    "Failed to parse email message data",
                    error=str(e),
                    data_obj=str(data_obj)[:500],
                    exc_info=True,
                )
                raise

            _process_email_message(email_message)
        elif msg_type == "grant.expired":
            _handle_grant_expired(data_obj)
        else:
            log.warning(
                "Found an unknown event type, skipping",
                event_type=msg_type,
                message_id=message_id,
            )
    except Exception as e:
        log.error(
            "Error processing Nylas notification",
            error=str(e),
            message_id=message_body.get("id"),
            task_id=self.request.id,
            exc_info=True,
        )
        # Re-raise to trigger retry
        raise


def _get_mailing_domain(email: str) -> str:
    """Extract the domain part from an email address."""
    return email.split("@")[-1]


def _extract_email_participants(email_message: NylasEmailMessage) -> EmailParticipants:
    """
    Extract all email participants from an email message.

    Args:
        email_message: The Nylas email message

    Returns:
        EmailParticipants object with all addresses normalized to lowercase
    """
    return EmailParticipants(
        sender=email_message.email_from[0].email.lower(),
        recipients_to=[addr.email.lower() for addr in email_message.email_to or []],
        recipients_cc=[addr.email.lower() for addr in email_message.email_cc or []],
        recipients_bcc=[addr.email.lower() for addr in email_message.email_bcc or []],
        reply_to_addresses=[
            addr.email.lower() for addr in email_message.email_reply_to or []
        ],
    )


def _get_credential_for_message(
    email_message: NylasEmailMessage,
) -> Optional[EmailCredentialNylas]:
    """
    Get the email credential for the given email message.

    Args:
        email_message: The Nylas email message

    Returns:
        The email credential if found, None otherwise
    """
    try:
        return EmailCredentialNylas.objects.get(grant_id=email_message.grant_id)
    except EmailCredentialNylas.DoesNotExist:
        log.error(
            "Could not find credential specified inside message. Check Nylas panel to see if exists.",
            email_message_id=email_message.id,
            grant_id=email_message.grant_id,
        )
        return None


def _determine_email_direction(email_from: str, credential_email: str) -> str:
    """
    Determine the direction of an email (incoming or outgoing).

    Args:
        email_from: The sender's email address
        credential_email: The credential's email address

    Returns:
        Communication.DIRECTION_OUTGOING or Communication.DIRECTION_INCOMING
    """
    return (
        Communication.DIRECTION_OUTGOING
        if email_from == credential_email
        else Communication.DIRECTION_INCOMING
    )


def _filter_counterparty_emails(
    participants: EmailParticipants,
    direction: str,
    cred: EmailCredentialNylas,
    message_id: str,
) -> List[str]:
    """
    Filter the list of counterparty emails based on email direction.

    Args:
        participants: All email participants
        direction: Email direction (incoming/outgoing)
        cred: Email credential
        message_id: Email message ID for logging

    Returns:
        List of unique counterparty email addresses
    """
    # For backward compatibility, maintain the original counterparty logic
    # but add additional counterparties from CC and BCC
    if direction == Communication.DIRECTION_OUTGOING:
        # Original logic: only TO addresses for outgoing emails
        primary_counterparties = participants.recipients_to
        # Additional counterparties from CC and BCC
        secondary_counterparties = (
            participants.recipients_cc + participants.recipients_bcc
        )
    else:
        # Original logic: FROM and REPLY-TO for incoming emails
        primary_counterparties = [participants.sender] + participants.reply_to_addresses
        # Additional counterparties from TO, CC, and BCC
        secondary_counterparties = (
            participants.recipients_to
            + participants.recipients_cc
            + participants.recipients_bcc
        )

    # Process primary counterparties first (maintains backward compatibility)
    counterparty_emails = primary_counterparties
    # Then add secondary counterparties
    additional_emails = [
        email for email in secondary_counterparties if email not in counterparty_emails
    ]

    # Log the additional emails being processed (for monitoring)
    if additional_emails:
        log.info(
            "Processing additional email participants from CC/BCC/TO fields",
            direction=direction,
            additional_emails=additional_emails,
            team_id=cred.team.id,
            message_id=message_id,
        )

    counterparty_emails.extend(additional_emails)
    return counterparty_emails


def _batch_fetch_supplier_data(
    counterparty_emails: List[str], team: Team
) -> SupplierLookupData:
    """
    Batch fetch supplier domains and contacts to avoid N+1 queries.

    Args:
        counterparty_emails: List of email addresses to look up
        team: The team to filter by

    Returns:
        Dictionary containing domain and email lookup maps
    """
    # Extract unique domains and emails
    unique_domains = {_get_mailing_domain(email) for email in counterparty_emails}
    unique_emails = {email for email in counterparty_emails}

    # Log performance metrics
    start_time = time.time()

    # Batch fetch all supplier domains for this team
    supplier_domains = SupplierDomain.objects.filter(
        domain__in=unique_domains, team=team
    ).select_related("supplier")

    # Create domain lookup dictionary
    domain_to_supplier_domain = {sd.domain: sd for sd in supplier_domains}

    # Batch fetch all supplier contacts for this team
    supplier_contacts = SupplierContact.objects.filter(
        email__in=unique_emails, supplier__team=team, archived_at__isnull=True
    ).select_related("supplier")

    # Create email lookup dictionary
    email_to_contact = {sc.email: sc for sc in supplier_contacts if sc.email}

    query_time = time.time() - start_time
    log.info(
        "Batch fetched supplier data",
        participant_count=len(counterparty_emails),
        unique_domains=len(unique_domains),
        unique_emails=len(unique_emails),
        query_time_seconds=query_time,
        team_id=team.id,
    )

    return SupplierLookupData(
        domain_to_supplier_domain=domain_to_supplier_domain,
        email_to_contact=email_to_contact,
    )


def _find_supplier_for_email(
    email: str, supplier_data: SupplierLookupData
) -> Optional[Supplier]:
    """
    Find the supplier associated with an email address.

    Args:
        email: Email address to look up
        supplier_data: Pre-fetched supplier lookup data

    Returns:
        The supplier if found and not archived, None otherwise
    """
    domain = _get_mailing_domain(email)

    # Use dictionary lookups instead of database queries
    supplier_domain = supplier_data["domain_to_supplier_domain"].get(domain)
    existing_contact = supplier_data["email_to_contact"].get(email)

    is_email_in_domain = supplier_domain and not supplier_domain.supplier.is_archived()
    is_email_in_contact = (
        existing_contact and not existing_contact.supplier.is_archived()
    )

    if not is_email_in_domain and not is_email_in_contact:
        return None

    # Determine which supplier to use
    if is_email_in_domain and supplier_domain:
        return supplier_domain.supplier
    elif is_email_in_contact and existing_contact:
        return existing_contact.supplier

    return None


def _process_counterparty_email(
    counterparty_email: str,
    cred: EmailCredentialNylas,
    email_message: NylasEmailMessage,
    supplier_data: SupplierLookupData,
) -> None:
    """
    Process a single counterparty email address.

    Args:
        counterparty_email: The email address to process
        cred: Email credential
        email_message: The full email message
        supplier_data: Pre-fetched supplier lookup data
    """
    supplier = _find_supplier_for_email(counterparty_email, supplier_data)

    if not supplier:
        log.warning(
            "Email does not match any supplier domain or supplier contact, skipping",
            counterparty_email=counterparty_email,
            domain=_get_mailing_domain(counterparty_email),
            team_id=cred.team.id,
        )
        return

    try:
        create_communication_and_documents(
            cred, supplier, email_message.model_dump(by_alias=True)
        )
    except Exception as e:
        log.error(
            "UNEXPECTED ERROR when creating communication and documents, make sure to handle this dropped message if the email was not created",
            error=e,
            message_id=email_message.id,
            grant_id=email_message.grant_id,
            exc_info=True,
        )


def _process_email_message(email_message: NylasEmailMessage) -> None:
    """
    Process an email message from Nylas webhook.

    This function:
    1. Determines the email direction (incoming/outgoing)
    2. Extracts all email participants (FROM, TO, CC, BCC, REPLY-TO)
    3. Checks if any participants match registered suppliers
    4. Creates a communication record for each matching supplier

    Args:
        email_message: The email message from Nylas
    """
    # Step 1: Get credential
    cred = _get_credential_for_message(email_message)
    if not cred:
        return

    # Step 2: Extract participants and determine direction
    participants = _extract_email_participants(email_message)
    direction = _determine_email_direction(participants.sender, cred.email_address)

    # Step 3: Filter counterparty emails list
    counterparty_emails = _filter_counterparty_emails(
        participants, direction, cred, email_message.id
    )

    # Step 4: Batch fetch supplier data
    supplier_data = _batch_fetch_supplier_data(counterparty_emails, cred.team)

    # Step 5: Process each counterparty
    for counterparty_email in counterparty_emails:
        _process_counterparty_email(
            counterparty_email, cred, email_message, supplier_data
        )


def _handle_grant_expired(data: NylasGrantExpiredDict) -> None:
    """
    Handle grant expiration events from Nylas.

    Args:
        data: The grant expiration data from Nylas
    """
    grant_id = data["grant_id"]
    try:
        cred = EmailCredentialNylas.objects.get(grant_id=grant_id)
    except EmailCredentialNylas.DoesNotExist:
        log.error(
            "Could not find credential that was expired",
            grant_id=grant_id,
        )
        return

    cred.status = NylasGrantStatus.INVALID
    cred.save(update_fields=["status"])
    log.info("Expired grant", grant_id=grant_id)
