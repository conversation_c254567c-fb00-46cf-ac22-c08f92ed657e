import os

from celery import Celery, signals
from django_structlog.celery.steps import DjangoStructLogInitStep
from dotenv import load_dotenv
from kombu import Queue

load_dotenv()

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

app = Celery("didero")

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object("django.conf:settings", namespace="CELERY")

# Load task modules from all registered Django apps.
app.steps["worker"].add(DjangoStructLogInitStep)
app.autodiscover_tasks()


@signals.setup_logging.connect
def config_loggers(*args, **kwags):
    from logging.config import dictConfig

    from django.conf import settings

    dictConfig(settings.LOGGING)


@app.task(bind=True, ignore_result=True)
def debug_task(self):
    print(f"Request: {self.request!r}")


# Define multiple queues. If we add any more, make sure to add them to the celery command in the Makefile.
app.conf.task_queues = (
    Queue("default", routing_key="default"),
    Queue("ai_workflows", routing_key="ai_workflows"),
    Queue("bulk_supplier_imports", routing_key="bulk_supplier_imports"),
    Queue("email_backfills", routing_key="email_backfills"),
    Queue("periodic_tasks", routing_key="periodic_tasks"),
    Queue("task_email_notifications", routing_key="task_email_notifications"),
    Queue("nylas_email_notifications", routing_key="nylas_email_notifications"),
)
app.conf.task_default_queue = "default"
app.conf.task_default_exchange = "default"
app.conf.task_default_routing_key = "default"
