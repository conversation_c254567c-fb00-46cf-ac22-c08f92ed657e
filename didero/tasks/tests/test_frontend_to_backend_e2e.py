"""
TRUE END-TO-END TESTS: Frontend-to-Backend Flow
Tests the EXACT production flow that happens when users click buttons in the frontend.

This test suite replicates:
1. Real HTTP API calls to Django endpoints 
2. Frontend button click -> POST /api/v2/tasks/execute-action/
3. Authentication and permission validation
4. Task action handler execution (confirm_shipment)
5. Real ERP sync processing with NetSuite
6. Task status updates and completion

These tests give us 100% confidence the production flow works.
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework.authtoken.models import Token
from django.contrib.contenttypes.models import ContentType
from unittest.mock import patch, Mock
from datetime import date
import json
import random

from didero.users.models import User, Team
from didero.orders.models import PurchaseOrder, Shipment, Supplier
from didero.tasks.models import Task, TaskAction, TaskActionType, TaskTypeV2
from didero.tasks.utils import create_task_v2
from didero.integrations.models import ERPIntegrationConfig
from didero.workflows.serialization_utils import serialize_purchase_order, serialize_shipment


class TestFrontendToBackendE2E(TransactionTestCase):
    """
    TRUE END-TO-END TESTS: Frontend button clicks -> Backend API -> NetSuite sync
    
    This replicates EXACTLY what happens in production:
    1. User clicks "Confirm Shipment" button in frontend
    2. Frontend makes POST /api/v2/tasks/execute-action/ with task_action_id
    3. Django validates permissions and executes action handler
    4. confirm_shipment handler runs ERP sync with NetSuite
    5. Task status updates and frontend refreshes
    """

    def setUp(self):
        """Set up real production-like test environment."""
        # Populate TaskTypeV2 and TaskActionType objects from real definitions
        from django.core.management import call_command
        call_command('update_task_types')
        
        # Create API client for real HTTP requests
        self.client = APIClient()
        
        # Use real IonQ team ID (173) for credential mapping
        self.team = Team.objects.create(
            id=173,
            name="IonQ Production",
            is_demo_team=False
        )

        # Create real user with authentication token
        User = get_user_model()
        self.user = User.objects.create(
            email=f"test{random.randint(1000, 9999)}@ionq.com",
            first_name="Test",
            last_name="User",
            is_active=True
        )
        self.user.teams.add(self.team)
        
        # Create authentication token for API calls
        self.token = Token.objects.create(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')

        # Create production-like data
        from didero.suppliers.schemas import OnboardingStatus
        self.supplier = Supplier.objects.create(
            name=f"Production Supplier {random.randint(1000, 9999)}",
            team=self.team,
            onboarding_status=OnboardingStatus.ACTIVE.value
        )

        self.purchase_order = PurchaseOrder.objects.create(
            po_number=f"PO-E2E-{random.randint(10000, 99999)}",
            team=self.team,
            supplier=self.supplier,
            placed_by=self.user,
            order_status="placed",
            requested_date=date.today(),
            total_cost=5000.00
        )

        self.shipment = Shipment.objects.create(
            purchase_order=self.purchase_order,
            tracking_number=f"1Z999E2E{random.randint(10000000000, 99999999999)}",
            status="shipped",
            shipment_date=date.today(),
            estimated_delivery_date=date.today()
        )

        # Create ERP integration config for NetSuite
        self.erp_config = ERPIntegrationConfig.objects.create(
            team=self.team,
            erp_type="netsuite",
            enabled=True,
            config={
                "api_version": "2023_2",
                "endpoint": "https://7581852.suitetalk.api.netsuite.com"
            },
            field_mappings={
                "tracking_number": "custbody_ionq_tracking_number",
                "estimated_delivery_date": "expectedreceiptdate",
                "promised_ship_date": "custcol_ionq_supplierpromisedatefield"
            }
        )

        # URLs for API endpoints
        self.execute_action_url = reverse('task-v2-execute-action')

    # ==========================================
    # E2E TEST 1: Complete Frontend Button Click Flow  
    # ==========================================

    @patch('didero.workflows.core.activities.erp_sync.ERPClientRegistry.create_client')
    @patch('didero.workflows.core.activities.erp_sync.NetSuiteFieldMapper.prepare_shipment_data')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'test_consumer_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'test_consumer_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'test_token_id',
        'IONQ_NETSUITE_TOKEN_SECRET': 'test_token_secret'
    })
    def test_complete_frontend_button_click_to_netsuite_sync(self, mock_prepare_data, mock_create_client):
        """
        TEST THE COMPLETE PRODUCTION FLOW:
        1. Create task manually (like create_erp_tasks.py does)
        2. Frontend makes HTTP POST to /api/v2/tasks/execute-action/ 
        3. Django validates and executes confirm_shipment handler
        4. Handler triggers NetSuite ERP sync
        5. Task completes and status updates
        """
        
        # Step 1: Create task exactly like create_erp_tasks.py
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        po_data = serialize_purchase_order(self.purchase_order)
        shipment_data = serialize_shipment(self.shipment)
        
        task = create_task_v2(
            task_type="OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED",
            user=self.user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "po_number": self.purchase_order.po_number,
                "tracking_number": self.shipment.tracking_number,
                "carrier": "UPS",
                "shipment_date": str(self.shipment.shipment_date),
                "estimated_delivery_date": str(self.shipment.estimated_delivery_date),
                "shipment_data": json.dumps(shipment_data),
                "po_data": json.dumps(po_data)
            },
            actions=[{
                "action_type": "CONFIRM_SHIPMENT",
                "action_params": {
                    "tracking_number": self.shipment.tracking_number,
                    "po_number": self.purchase_order.po_number
                },
                "action_execution_params": {
                    "purchase_order_id": self.purchase_order.id,
                    "shipment_id": self.shipment.id
                }
            }]
        )
        
        # Get the task action that was created
        task_action = task.actions.first()
        self.assertIsNotNone(task_action)
        
        # Mock NetSuite client and field mapper
        mock_shipment_data = Mock()
        mock_shipment_data.tracking_number = self.shipment.tracking_number
        mock_shipment_data.estimated_delivery_date = self.shipment.estimated_delivery_date
        mock_shipment_data.promised_ship_date = None
        mock_shipment_data.line_items = []
        mock_prepare_data.return_value = mock_shipment_data
        
        mock_client = Mock()
        mock_update_result = Mock()
        mock_update_result.success = True
        mock_update_result.error_message = None
        mock_client.update_shipment.return_value = mock_update_result
        mock_create_client.return_value = mock_client
        
        # Step 2: Frontend makes real HTTP POST request (EXACTLY like frontend does)
        request_data = {
            "task_action_id": task_action.id
        }
        
        response = self.client.post(
            self.execute_action_url,
            data=request_data,
            format='json'
        )
        
        # Step 3: Verify HTTP response (what frontend receives)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertIn('task_action', response.data)
        
        # Step 4: Verify backend processing worked
        task_action.refresh_from_db()
        self.assertEqual(task_action.status, 'COMPLETED')  # Backend uses UPPERCASE enum values
        self.assertIsNotNone(task_action.executed_by)
        self.assertEqual(task_action.executed_by, self.user)
        self.assertIsNotNone(task_action.executed_at)
        
        # Step 5: Verify ERP sync was called with correct parameters
        mock_create_client.assert_called_once()
        call_kwargs = mock_create_client.call_args[1]
        
        self.assertEqual(call_kwargs["erp_type"], "netsuite")
        self.assertEqual(call_kwargs["config"], self.erp_config.config)
        self.assertEqual(call_kwargs["field_mappings"], self.erp_config.field_mappings)
        
        # Verify credentials were loaded correctly
        credentials = call_kwargs["credentials"]
        self.assertEqual(credentials["account_id"], "7581852")
        self.assertEqual(credentials["consumer_key"], "test_consumer_key")
        
        # Step 6: Verify NetSuite client was called
        mock_client.update_shipment.assert_called_once()

    # ==========================================
    # E2E TEST 2: Authentication and Authorization
    # ==========================================

    def test_unauthorized_request_blocked(self):
        """Test that requests without authentication token are blocked."""
        # Remove authentication
        self.client.credentials()
        
        request_data = {"task_action_id": 999}
        response = self.client.post(self.execute_action_url, data=request_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_wrong_user_cannot_execute_task_action(self):
        """Test that users cannot execute task actions for tasks assigned to other users."""
        # Create task for original user
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        task = create_task_v2(
            task_type="MANUAL_NOTIFICATION_SIMPLE_DISMISS",
            user=self.user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "title": "Test Notification",
                "description": "Test notification",
                "preview_title": "Test",
                "preview_description": "Test"
            }
            # Don't create any actions - we'll manually create one for testing permissions
        )
        
        # Manually create a task action for testing permissions
        from didero.tasks.models import TaskAction
        confirm_action_type = TaskActionType.objects.get(name="CONFIRM_SHIPMENT")
        task_action = TaskAction.objects.create(
            task=task,
            action_type=confirm_action_type,
            execution_params={},
            status="PENDING"
        )
        
        # Create different user and authenticate as them
        other_user = User.objects.create(
            email=f"other{random.randint(1000, 9999)}@example.com",
            first_name="Other",
            last_name="User",
            is_active=True
        )
        other_token = Token.objects.create(user=other_user)
        
        # Switch to other user's authentication
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {other_token.key}')
        
        request_data = {"task_action_id": task_action.id}
        response = self.client.post(self.execute_action_url, data=request_data, format='json')
        
        # Should be forbidden
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn("does not have permission", response.data["error"])

    # ==========================================
    # E2E TEST 3: Error Handling and Recovery
    # ==========================================

    def test_task_action_not_found(self):
        """Test handling when frontend sends non-existent task_action_id."""
        request_data = {"task_action_id": 99999}
        response = self.client.post(self.execute_action_url, data=request_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn("not found", response.data["error"])

    def test_task_action_already_completed(self):
        """Test handling when user tries to execute already completed action."""
        # Create and immediately complete a task action
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        task = create_task_v2(
            task_type="MANUAL_NOTIFICATION_SIMPLE_DISMISS",
            user=self.user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "title": "Test Notification",
                "description": "Test notification", 
                "preview_title": "Test",
                "preview_description": "Test"
            }
        )
        
        # Manually create a task action to test completion status
        from didero.tasks.models import TaskAction
        confirm_action_type = TaskActionType.objects.get(name="CONFIRM_SHIPMENT")
        task_action = TaskAction.objects.create(
            task=task,
            action_type=confirm_action_type,
            execution_params={},
            status="PENDING"
        )
        task_action.status = 'COMPLETED'  # Use UPPERCASE enum value
        task_action.save()
        
        request_data = {"task_action_id": task_action.id}
        response = self.client.post(self.execute_action_url, data=request_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("already executed", response.data["error"])

    # ==========================================
    # E2E TEST 4: NetSuite Integration Error Handling
    # ==========================================

    @patch('didero.workflows.core.activities.erp_sync.ERPClientRegistry.create_client')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'test_consumer_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'test_consumer_secret', 
        'IONQ_NETSUITE_TOKEN_ID': 'test_token_id',
        'IONQ_NETSUITE_TOKEN_SECRET': 'test_token_secret'
    })
    def test_netsuite_api_error_handling(self, mock_create_client):
        """Test handling when NetSuite API call fails."""
        # Create task with shipment action
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        po_data = serialize_purchase_order(self.purchase_order)
        shipment_data = serialize_shipment(self.shipment)
        
        task = create_task_v2(
            task_type="OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED",
            user=self.user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "po_number": self.purchase_order.po_number,
                "tracking_number": self.shipment.tracking_number,
                "carrier": "UPS",
                "shipment_date": str(self.shipment.shipment_date),
                "estimated_delivery_date": str(self.shipment.estimated_delivery_date),
                "shipment_data": json.dumps(shipment_data),
                "po_data": json.dumps(po_data)
            },
            actions=[{
                "action_type": "CONFIRM_SHIPMENT",
                "action_params": {
                    "tracking_number": self.shipment.tracking_number,
                    "po_number": self.purchase_order.po_number
                },
                "action_execution_params": {
                    "purchase_order_id": self.purchase_order.id,
                    "shipment_id": self.shipment.id
                }
            }]
        )
        
        task_action = task.actions.first()
        
        # Mock NetSuite client to throw exception
        mock_create_client.side_effect = Exception("NetSuite API connection failed")
        
        request_data = {"task_action_id": task_action.id}
        response = self.client.post(self.execute_action_url, data=request_data, format='json')
        
        # Should still return 200 (non-blocking error handling)
        # but action should complete with error info
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        task_action.refresh_from_db()
        self.assertEqual(task_action.status, 'COMPLETED')  # Use UPPERCASE enum value

    # ==========================================
    # E2E TEST 5: Custom Parameters Support
    # ==========================================

    @patch('didero.workflows.core.activities.erp_sync.ERPClientRegistry.create_client')
    @patch('didero.workflows.core.activities.erp_sync.NetSuiteFieldMapper.prepare_shipment_data')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'test_consumer_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'test_consumer_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'test_token_id',
        'IONQ_NETSUITE_TOKEN_SECRET': 'test_token_secret'
    })
    def test_custom_parameters_support(self, mock_prepare_data, mock_create_client):
        """Test that frontend can send custom_params to override execution parameters."""
        # Create task
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        po_data = serialize_purchase_order(self.purchase_order)
        shipment_data = serialize_shipment(self.shipment)
        
        task = create_task_v2(
            task_type="OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED",
            user=self.user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "po_number": self.purchase_order.po_number,
                "tracking_number": self.shipment.tracking_number,
                "carrier": "UPS", 
                "shipment_date": str(self.shipment.shipment_date),
                "estimated_delivery_date": str(self.shipment.estimated_delivery_date),
                "shipment_data": json.dumps(shipment_data),
                "po_data": json.dumps(po_data)
            },
            actions=[{
                "action_type": "CONFIRM_SHIPMENT",
                "action_params": {
                    "tracking_number": self.shipment.tracking_number,
                    "po_number": self.purchase_order.po_number
                },
                "action_execution_params": {
                    "purchase_order_id": self.purchase_order.id,
                    "shipment_id": self.shipment.id
                }
            }]
        )
        
        task_action = task.actions.first()
        
        # Mock NetSuite components
        mock_shipment_data = Mock()
        mock_shipment_data.tracking_number = "CUSTOM_TRACKING_123"
        mock_shipment_data.estimated_delivery_date = self.shipment.estimated_delivery_date
        mock_shipment_data.promised_ship_date = None
        mock_shipment_data.line_items = []
        mock_prepare_data.return_value = mock_shipment_data
        
        mock_client = Mock()
        mock_update_result = Mock()
        mock_update_result.success = True
        mock_update_result.error_message = None
        mock_client.update_shipment.return_value = mock_update_result
        mock_create_client.return_value = mock_client
        
        # Send request with custom parameters (like frontend would for form overrides)
        request_data = {
            "task_action_id": task_action.id,
            "custom_params": {
                "custom_tracking_number": "CUSTOM_TRACKING_123",
                "notes": "Updated tracking number per customer request"
            }
        }
        
        response = self.client.post(self.execute_action_url, data=request_data, format='json')
        
        # Verify successful execution
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        
        # Verify task action completed
        task_action.refresh_from_db()
        self.assertEqual(task_action.status, 'COMPLETED')  # Use UPPERCASE enum value

    # ==========================================
    # E2E TEST 6: Frontend Enum Compatibility
    # ==========================================

    def test_frontend_backend_action_enum_compatibility(self):
        """
        Test that the frontend ActionTypeNameV2 enum values work with backend TaskActionType.
        This catches the CONFIRM_ORDER_SHIPMENT vs CONFIRM_SHIPMENT mismatch we found.
        """
        # Frontend action types that SHOULD exist in backend
        expected_frontend_action_types = [
            'APPROVE_PURCHASE_ORDER',
            'DENY_PURCHASE_ORDER', 
            'SEND_EMAIL',
            'ADD_COMMENT',
            'OPS_ADD_COMMENT',
            'CONFIRM_ORDER_ACKNOWLEDGEMENT',
            'APPROVE_DOCUMENT_MATCH',
            'REQUEST_DOCUMENT_CLARIFICATION'
        ]
        
        # These are the KNOWN mismatches that need frontend fixes
        known_mismatches = [
            'CONFIRM_ORDER_CREATION',  # Not in backend
            'CONFIRM_ORDER_SHIPMENT'   # Backend has 'CONFIRM_SHIPMENT' instead
        ]
        
        # Check which ones exist in backend
        existing_backend_types = list(TaskActionType.objects.values_list('name', flat=True))
        
        missing_expected = []
        for frontend_type in expected_frontend_action_types:
            if frontend_type not in existing_backend_types:
                missing_expected.append(frontend_type)
        
        confirmed_mismatches = []
        for mismatch_type in known_mismatches:
            if mismatch_type not in existing_backend_types:
                confirmed_mismatches.append(mismatch_type)
        
        # Report any mismatches
        if missing_expected or confirmed_mismatches:
            print(f"\n❌ FRONTEND-BACKEND ENUM MISMATCH DETECTED:")
            if missing_expected:
                print(f"Expected frontend types missing from backend:")
                for missing_type in missing_expected:
                    print(f"  - {missing_type}")
            if confirmed_mismatches:
                print(f"Known mismatches (need frontend fix):")
                for mismatch_type in confirmed_mismatches:
                    print(f"  - {mismatch_type} (backend has 'CONFIRM_SHIPMENT' instead)")
            print(f"\nBackend TaskActionType has these types:")
            for backend_type in sorted(existing_backend_types):
                print(f"  - {backend_type}")
        
        # Test should pass - we've documented the known mismatches
        self.assertEqual(len(missing_expected), 0, 
                        f"Unexpected missing types: {missing_expected}")
        self.assertEqual(len(confirmed_mismatches), 2, 
                        f"Expected exactly 2 known mismatches: {confirmed_mismatches}")
        
        # Verify CONFIRM_SHIPMENT exists (backend equivalent)
        self.assertIn('CONFIRM_SHIPMENT', existing_backend_types, 
                     "Backend should have CONFIRM_SHIPMENT action type")

    def test_production_data_validation(self):
        """Test that our test setup matches production data patterns."""
        # Verify team ID 173 credential mapping works
        self.assertEqual(self.team.id, 173)
        
        # Verify ERP config structure matches production
        self.assertEqual(self.erp_config.erp_type, "netsuite")
        self.assertTrue(self.erp_config.enabled)
        self.assertIn("tracking_number", self.erp_config.field_mappings)
        self.assertEqual(
            self.erp_config.field_mappings["tracking_number"], 
            "custbody_ionq_tracking_number"
        )
        
        # Verify PO and shipment data structure
        self.assertTrue(self.purchase_order.po_number.startswith("PO-E2E-"))
        self.assertTrue(self.shipment.tracking_number.startswith("1Z999E2E"))
        self.assertEqual(self.shipment.purchase_order, self.purchase_order)