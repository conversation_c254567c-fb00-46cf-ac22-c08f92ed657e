"""
Unit tests for manual ERP task flow components.

Tests each component in isolation with mocked dependencies.
These tests verify the internal logic of each function without external dependencies.
"""

from unittest.mock import Mock, patch, MagicMock
import unittest
from django.test import TestCase
from datetime import datetime, date
import asyncio
from asgiref.sync import async_to_sync

from didero.tasks.schemas import ActionHandlerContext, ActionHandlerResult
from didero.tasks.actions import confirm_shipment
from didero.workflows.core.activities.erp_sync import (
    sync_erp_purchase_order,
    get_erp_notification_recipient_guaranteed,
    create_erp_sync_success_task,
    create_erp_sync_error_task
)
from didero.integrations.erp.credentials import SimpleEnvCredentialProvider
from didero.integrations.erp.clients.netsuite import NetSuiteClient


class TestManualERPFlowUnitTests(TestCase):
    """
    Unit tests for individual components in the manual ERP task flow.
    Each test isolates a specific function with mocked dependencies.
    """

    def setUp(self):
        """Set up test fixtures with realistic data."""
        self.test_shipment_id = 12345
        self.test_po_id = 67890
        self.test_team_id = 173  # IonQ team ID
        self.test_po_number = "PO12345"
        
        # Mock models
        self.mock_purchase_order = Mock()
        self.mock_purchase_order.id = self.test_po_id
        self.mock_purchase_order.po_number = self.test_po_number
        self.mock_purchase_order.team.id = self.test_team_id
        self.mock_purchase_order.team.name = "IonQ"
        self.mock_purchase_order.requested_date = date(2024, 1, 15)
        
        self.mock_shipment = Mock()
        self.mock_shipment.id = self.test_shipment_id
        self.mock_shipment.purchase_order = self.mock_purchase_order
        self.mock_shipment.tracking_number = "1Z999AA10123456784"
        self.mock_shipment.estimated_delivery_date = date(2024, 1, 20)

    # ==========================================
    # UNIT TEST 1: Action Handler Parameter Validation
    # ==========================================
    
    def test_confirm_shipment_missing_parameters(self):
        """Test that confirm_shipment properly validates required parameters."""
        handler_context = ActionHandlerContext(task_action_id=1)
        
        # Test missing purchase_order_id
        result = confirm_shipment(handler_context, {"shipment_id": 123})
        self.assertIsNotNone(result.errors)
        self.assertIn("Missing required parameter: purchase_order_id", result.errors)
        
        # Test missing shipment_id
        result = confirm_shipment(handler_context, {"purchase_order_id": 456})
        self.assertIsNotNone(result.errors)
        self.assertIn("Missing required parameter: shipment_id", result.errors)
        
        # Test empty params
        result = confirm_shipment(handler_context, {})
        self.assertIsNotNone(result.errors)
        self.assertTrue(any("Missing required parameter" in error for error in result.errors))

    # ==========================================
    # UNIT TEST 2: Model Validation
    # ==========================================
    
    @patch('didero.orders.models.PurchaseOrder.objects')
    @patch('didero.orders.models.Shipment.objects')
    @patch('didero.integrations.models.ERPIntegrationConfig.objects')
    def test_confirm_shipment_model_validation(self, mock_erp_config, mock_shipment_objects, mock_po_objects):
        """Test that confirm_shipment properly validates model existence."""
        handler_context = ActionHandlerContext(task_action_id=1)
        params = {"purchase_order_id": 999, "shipment_id": 888}
        
        # Import the actual Django exceptions that the code expects
        from didero.orders.models import PurchaseOrder, Shipment
        
        # Test PO not found - use the correct Django exception
        mock_po_objects.select_related.return_value.get.side_effect = PurchaseOrder.DoesNotExist("Purchase order not found")
        
        result = confirm_shipment(handler_context, params)
        self.assertIsNotNone(result.errors)
        self.assertIn("Purchase order 999 not found", result.errors)
        
        # Test shipment not found - clear PO exception and set shipment exception
        mock_po_objects.select_related.return_value.get.side_effect = None  # Clear the exception
        mock_po_objects.select_related.return_value.get.return_value = self.mock_purchase_order
        mock_shipment_objects.get.side_effect = Shipment.DoesNotExist("Shipment not found")
        
        result = confirm_shipment(handler_context, params)
        self.assertIsNotNone(result.errors)
        self.assertIn("Shipment 888 not found", result.errors)

    # ==========================================
    # UNIT TEST 3: ERP Integration Detection
    # ==========================================
    
    @patch('didero.orders.models.PurchaseOrder.objects')
    @patch('didero.orders.models.Shipment.objects')
    @patch('didero.integrations.models.ERPIntegrationConfig.objects')
    @patch('asgiref.sync.async_to_sync')
    def test_confirm_shipment_erp_integration_detection(self, mock_async_to_sync, mock_erp_config, mock_shipment_objects, mock_po_objects):
        """Test ERP integration detection logic."""
        handler_context = ActionHandlerContext(task_action_id=1)
        params = {"purchase_order_id": self.test_po_id, "shipment_id": self.test_shipment_id}
        
        # Setup mocks
        mock_po_objects.select_related.return_value.get.return_value = self.mock_purchase_order
        mock_shipment_objects.get.return_value = self.mock_shipment
        
        # Test with ERP integration enabled
        mock_erp_config.filter.return_value.exists.return_value = True
        mock_sync_result = {"success": True, "erp_system": "netsuite", "synced_fields": ["tracking_number"]}
        mock_async_to_sync.return_value.return_value = mock_sync_result
        
        result = confirm_shipment(handler_context, params)
        
        # Verify ERP sync was called
        mock_async_to_sync.assert_called()
        
        # Verify successful result
        self.assertIsNone(result.errors)
        self.assertIn("Shipment confirmed and synced to NETSUITE", result.result["message"])

    # ==========================================
    # UNIT TEST 4: Credential Provider
    # ==========================================
    
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'test_consumer_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'test_consumer_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'test_token_id',
        'IONQ_NETSUITE_TOKEN_SECRET': 'test_token_secret'
    })
    def test_credential_provider_ionq_team(self):
        """Test that credential provider correctly maps IonQ team ID to environment variables."""
        provider = SimpleEnvCredentialProvider()
        
        # Test IonQ production team ID (173)
        credentials = provider.get_credentials("173", "netsuite")
        
        self.assertEqual(credentials["account_id"], "7581852")
        self.assertEqual(credentials["consumer_key"], "test_consumer_key")
        self.assertEqual(credentials["consumer_secret"], "test_consumer_secret")
        self.assertEqual(credentials["token_id"], "test_token_id")
        self.assertEqual(credentials["token_secret"], "test_token_secret")

    @patch.dict('os.environ', {}, clear=True)
    def test_credential_provider_missing_credentials(self):
        """Test credential provider behavior with missing environment variables."""
        provider = SimpleEnvCredentialProvider()
        
        # Test with missing credentials
        credentials = provider.get_credentials("173", "netsuite")
        
        # Should return empty dict for missing credentials
        self.assertEqual(credentials, {})

    def test_credential_provider_validation(self):
        """Test credential validation logic."""
        provider = SimpleEnvCredentialProvider()
        
        # Test complete credentials
        complete_creds = {
            "account_id": "test",
            "consumer_key": "test",
            "consumer_secret": "test", 
            "token_id": "test",
            "token_secret": "test"
        }
        self.assertTrue(provider.validate_credentials(complete_creds, "netsuite"))
        
        # Test incomplete credentials
        incomplete_creds = {"account_id": "test"}
        self.assertFalse(provider.validate_credentials(incomplete_creds, "netsuite"))

    # ==========================================
    # UNIT TEST 5: NetSuite Client Initialization
    # ==========================================
    
    def test_netsuite_client_initialization(self):
        """Test NetSuite client initialization with proper credentials."""
        credentials = {
            "account_id": "7581852_SB1",
            "consumer_key": "test_key",
            "consumer_secret": "test_secret",
            "token_id": "test_token",
            "token_secret": "test_token_secret"
        }
        
        # Should not raise exception with complete credentials
        try:
            client = NetSuiteClient(credentials)
            self.assertIsNotNone(client)
            self.assertEqual(client.credentials["account_id"], "7581852_SB1")
        except Exception as e:
            self.fail(f"NetSuite client initialization failed with valid credentials: {e}")

    def test_netsuite_client_missing_credentials(self):
        """Test NetSuite client behavior with missing credentials."""
        incomplete_credentials = {"account_id": "7581852"}
        
        # Should raise ValueError for missing credentials
        with self.assertRaises(ValueError) as context:
            NetSuiteClient(incomplete_credentials)
        
        self.assertIn("Missing required credentials", str(context.exception))

    def test_netsuite_endpoint_generation(self):
        """Test NetSuite endpoint URL generation."""
        credentials = {
            "account_id": "7581852_SB1",
            "consumer_key": "test_key",
            "consumer_secret": "test_secret",
            "token_id": "test_token",
            "token_secret": "test_token_secret"
        }
        
        client = NetSuiteClient(credentials)
        
        # Test endpoint format
        expected_endpoint = "https://7581852-sb1.suitetalk.api.netsuite.com/services/NetSuitePort_2023_2"
        self.assertEqual(client.endpoint, expected_endpoint)

    # ==========================================
    # UNIT TEST 6: ERP Sync Activity Function
    # ==========================================
    
    @unittest.skip("Complex async mocking - move to integration tests")
    @patch('didero.workflows.core.activities.erp_sync.sync_to_async')
    @patch('didero.workflows.core.activities.erp_sync.NetSuiteFieldMapper')
    @patch('didero.workflows.core.activities.erp_sync.ERPClientRegistry')
    @patch('didero.integrations.erp.credentials.SimpleEnvCredentialProvider')
    @patch('didero.workflows.core.activities.erp_sync.create_erp_sync_success_task')
    def test_sync_erp_purchase_order_success_flow(self, mock_create_task, mock_cred_provider, mock_registry, mock_field_mapper, mock_sync_to_async):
        """Test successful ERP sync flow in isolation."""
        # Setup mocks
        mock_shipment = Mock()
        mock_shipment.id = self.test_shipment_id
        mock_shipment.purchase_order = self.mock_purchase_order
        mock_shipment.tracking_number = "1Z999AA10123456784"
        
        mock_sync_to_async.return_value = lambda: mock_shipment
        
        # Mock ERP config
        mock_erp_config = Mock()
        mock_erp_config.erp_type = "netsuite"
        mock_erp_config.field_mappings = {}
        mock_erp_config.config = {}
        
        # Mock shipment data
        mock_shipment_data = Mock()
        mock_shipment_data.tracking_number = "1Z999AA10123456784"
        mock_shipment_data.estimated_delivery_date = None
        mock_shipment_data.promised_ship_date = None
        mock_shipment_data.line_items = []
        
        mock_field_mapper.prepare_shipment_data.return_value = mock_shipment_data
        
        # Mock credentials
        mock_provider = Mock()
        mock_provider.get_credentials.return_value = {"account_id": "test"}
        mock_cred_provider.return_value = mock_provider
        
        # Mock client
        mock_client = Mock()
        mock_update_result = Mock()
        mock_update_result.success = True
        mock_update_result.error_message = None
        mock_client.update_shipment.return_value = mock_update_result
        mock_registry.create_client.return_value = mock_client
        
        # Mock ERP sync record
        mock_erp_sync = Mock()
        
        # Setup complex sync_to_async mocking
        def sync_to_async_side_effect(func):
            if "get_or_create" in str(func):
                return lambda: (mock_erp_sync, True)
            elif "get" in str(func) and "ERPIntegrationConfig" in str(func):
                return lambda: mock_erp_config
            elif "select_related" in str(func):
                return lambda: mock_shipment
            else:
                return lambda: None
        
        mock_sync_to_async.side_effect = sync_to_async_side_effect
        
        # Execute test
        params = {
            "shipment_id": self.test_shipment_id,
            "team_id": self.test_team_id,
            "sync_mode": "manual"
        }
        
        result = async_to_sync(sync_erp_purchase_order)(params)
        
        # Verify successful result
        self.assertTrue(result["success"])
        self.assertEqual(result["erp_system"], "netsuite")
        self.assertIn("tracking_number", result["synced_fields"])

    # ==========================================
    # UNIT TEST 7: Error Handling
    # ==========================================
    
    @patch('didero.workflows.core.activities.erp_sync.sync_to_async')
    def test_sync_erp_purchase_order_shipment_not_found(self, mock_sync_to_async):
        """Test ERP sync behavior when shipment is not found."""
        # Mock shipment not found
        def sync_to_async_side_effect(func):
            raise Exception("Shipment not found")
        
        mock_sync_to_async.side_effect = sync_to_async_side_effect
        
        params = {
            "shipment_id": 99999,  # Non-existent shipment
            "team_id": self.test_team_id,
            "sync_mode": "manual"
        }
        
        result = async_to_sync(sync_erp_purchase_order)(params)
        
        # Should handle error gracefully
        self.assertFalse(result["success"])
        self.assertIn("error_message", result)

    @unittest.skip("Complex async mocking - move to integration tests")
    @patch('didero.workflows.core.activities.erp_sync.sync_to_async')
    @patch('didero.workflows.core.activities.erp_sync.create_erp_sync_error_task')
    def test_sync_erp_purchase_order_no_erp_config(self, mock_create_error_task, mock_sync_to_async):
        """Test ERP sync behavior when team has no ERP configuration."""
        # Mock shipment loading
        mock_shipment = Mock()
        mock_shipment.id = self.test_shipment_id
        mock_shipment.purchase_order = self.mock_purchase_order
        
        # Mock ERP sync record
        mock_erp_sync = Mock()
        
        def sync_to_async_side_effect(func):
            if "select_related" in str(func):
                return lambda: mock_shipment
            elif "get_or_create" in str(func):
                return lambda: (mock_erp_sync, True)
            elif "ERPIntegrationConfig" in str(func):
                # Return a function that raises when called
                def erp_config_getter():
                    from didero.integrations.models import ERPIntegrationConfig
                    raise ERPIntegrationConfig.DoesNotExist()
                return erp_config_getter
            else:
                return lambda: None
        
        mock_sync_to_async.side_effect = sync_to_async_side_effect
        
        params = {
            "shipment_id": self.test_shipment_id,
            "team_id": self.test_team_id,
            "sync_mode": "manual"
        }
        
        result = async_to_sync(sync_erp_purchase_order)(params)
        
        # Should indicate no ERP configuration
        self.assertTrue(result["success"])  # Not an error, just not configured
        self.assertEqual(result["erp_system"], "none")
        self.assertEqual(result["synced_fields"], [])

    # ==========================================
    # UNIT TEST 8: OAuth Signature Generation
    # ==========================================
    
    def test_netsuite_oauth_signature_generation(self):
        """Test OAuth signature generation produces consistent results."""
        credentials = {
            "account_id": "7581852",
            "consumer_key": "test_key",
            "consumer_secret": "test_secret",
            "token_id": "test_token",
            "token_secret": "test_token_secret"
        }
        
        client = NetSuiteClient(credentials)
        
        # Generate signature
        timestamp, nonce, signature = client._generate_oauth_signature()
        
        # Verify components
        self.assertIsInstance(timestamp, str)
        self.assertIsInstance(nonce, str)
        self.assertIsInstance(signature, str)
        self.assertEqual(len(nonce), 20)  # Should be 20 characters
        self.assertTrue(len(signature) > 0)  # Should have signature

    # ==========================================
    # UNIT TEST 9: SOAP Envelope Generation
    # ==========================================
    
    def test_netsuite_soap_envelope_creation(self):
        """Test SOAP envelope creation includes required elements."""
        credentials = {
            "account_id": "7581852",
            "consumer_key": "test_key",
            "consumer_secret": "test_secret",
            "token_id": "test_token",
            "token_secret": "test_token_secret"
        }
        
        client = NetSuiteClient(credentials)
        
        # Create envelope
        body_content = "<test>test content</test>"
        envelope = client._create_soap_envelope(body_content)
        
        # Verify envelope structure
        self.assertIn('xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"', envelope)
        self.assertIn('xmlns:platformCore="urn:core_2023_2.platform.webservices.netsuite.com"', envelope)
        self.assertIn('<platformMsgs:tokenPassport>', envelope)
        self.assertIn('<platformCore:account>7581852</platformCore:account>', envelope)
        self.assertIn('<test>test content</test>', envelope)

    # ==========================================
    # UNIT TEST 10: Notification Recipient Logic
    # ==========================================
    
    @patch('didero.workflows.core.activities.erp_sync.sync_to_async')
    @patch('didero.workflows.core.activities.erp_sync.get_didero_ai_user_email')
    def test_get_erp_notification_recipient_guaranteed(self, mock_get_ai_email, mock_sync_to_async):
        """Test notification recipient selection logic."""
        # Test with placed_by user
        mock_user = Mock()
        mock_user.email = "<EMAIL>"
        
        mock_shipment = Mock()
        mock_shipment.id = self.test_shipment_id
        mock_shipment.purchase_order.placed_by = mock_user
        
        result = async_to_sync(get_erp_notification_recipient_guaranteed)(mock_shipment)
        self.assertEqual(result, mock_user)
        
        # Test with no placed_by (should use AI fallback)
        mock_shipment.purchase_order.placed_by = None
        mock_shipment.purchase_order.team.default_requestor = None
        
        mock_ai_user = Mock()
        mock_ai_user.email = "<EMAIL>"
        
        # Mock get_didero_ai_user_email function call
        async def mock_get_ai_email_func(team):
            return "<EMAIL>"
        
        # Mock User.objects.get_or_create call
        async def mock_user_get_or_create(*args, **kwargs):
            return (mock_ai_user, False)
        
        # Set up sync_to_async to return different functions based on what's being wrapped
        def sync_to_async_side_effect(func):
            if "get_didero_ai_user_email" in str(func):
                return mock_get_ai_email_func
            elif "get_or_create" in str(func):
                return mock_user_get_or_create
            else:
                return lambda *args, **kwargs: None
        
        mock_sync_to_async.side_effect = sync_to_async_side_effect
        
        result = async_to_sync(get_erp_notification_recipient_guaranteed)(mock_shipment)
        self.assertEqual(result, mock_ai_user)


if __name__ == "__main__":
    # Run with Django test runner
    import unittest
    unittest.main()