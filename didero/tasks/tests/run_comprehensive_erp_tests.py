#!/usr/bin/env python3
"""
Comprehensive test runner for manual ERP task flow.

This script orchestrates the complete testing strategy across all layers.
It provides detailed reporting and identifies exactly where failures occur.

Usage:
    python run_comprehensive_erp_tests.py [--layer unit|integration|e2e|production]
    python run_comprehensive_erp_tests.py [--test specific_test_name]
    python run_comprehensive_erp_tests.py [--production-check]
"""

import sys
import os
import subprocess
import time
import argparse
import json
from datetime import datetime
from typing import Dict, List, Any


class ERPTestRunner:
    """Comprehensive test runner for ERP flow validation."""
    
    def __init__(self):
        self.results = {
            "unit": {"passed": 0, "failed": 0, "errors": []},
            "integration": {"passed": 0, "failed": 0, "errors": []},
            "e2e": {"passed": 0, "failed": 0, "errors": []},
            "production": {"passed": 0, "failed": 0, "errors": []}
        }
        self.start_time = datetime.now()
        
    def run_layer(self, layer: str) -> bool:
        """Run tests for a specific layer."""
        print(f"\n{'='*60}")
        print(f"🧪 RUNNING {layer.upper()} TESTS")
        print(f"{'='*60}")
        
        test_file = f"test_manual_erp_flow_{layer}.py"
        
        try:
            # Run Django tests with detailed output
            cmd = [
                "python", "manage.py", "test", 
                f"didero.tasks.tests.test_manual_erp_flow_{layer}",
                "--verbosity=2",
                "--keepdb"
            ]
            
            print(f"Executing: {' '.join(cmd)}")
            
            start_time = time.time()
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd="/Users/<USER>/Desktop/work/main/didero-api"
            )
            duration = time.time() - start_time
            
            print(f"\n📊 {layer.upper()} RESULTS (took {duration:.2f}s):")
            print("-" * 40)
            
            if result.returncode == 0:
                print(f"✅ ALL {layer.upper()} TESTS PASSED")
                self.results[layer]["passed"] = self._count_tests_from_output(result.stdout)
                return True
            else:
                print(f"❌ {layer.upper()} TESTS FAILED")
                print("\nSTDOUT:")
                print(result.stdout)
                print("\nSTDERR:")
                print(result.stderr)
                
                # Parse failures
                self._parse_failures(layer, result.stdout, result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ ERROR RUNNING {layer.upper()} TESTS: {e}")
            self.results[layer]["errors"].append(str(e))
            return False
    
    def _count_tests_from_output(self, output: str) -> int:
        """Count passed tests from Django test output."""
        lines = output.split('\n')
        for line in lines:
            if "Ran " in line and " tests in " in line:
                # Look for pattern like "Ran 10 tests in 2.3s"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "Ran" and i + 1 < len(parts):
                        try:
                            return int(parts[i + 1])
                        except ValueError:
                            continue
        return 0
    
    def _parse_failures(self, layer: str, stdout: str, stderr: str):
        """Parse test failures and errors."""
        lines = (stdout + "\n" + stderr).split('\n')
        
        current_test = None
        current_error = []
        
        for line in lines:
            if "FAIL:" in line and "test_" in line:
                if current_test and current_error:
                    self.results[layer]["errors"].append({
                        "test": current_test,
                        "error": "\n".join(current_error)
                    })
                
                # Extract test name from Django format: "FAIL: test_name (module.class)"
                parts = line.split("test_")
                if len(parts) > 1:
                    current_test = "test_" + parts[1].split()[0]
                    current_error = []
                    self.results[layer]["failed"] += 1
                
            elif "ERROR:" in line and "test_" in line:
                if current_test and current_error:
                    self.results[layer]["errors"].append({
                        "test": current_test,
                        "error": "\n".join(current_error)
                    })
                
                # Extract test name from Django format
                parts = line.split("test_")
                if len(parts) > 1:
                    current_test = "test_" + parts[1].split()[0]
                    current_error = []
                    self.results[layer]["failed"] += 1
                
            elif line.startswith("    ") or line.startswith("AssertionError") or line.startswith("ImportError"):
                current_error.append(line)
        
        # Add last error if exists
        if current_test and current_error:
            self.results[layer]["errors"].append({
                "test": current_test,
                "error": "\n".join(current_error)
            })
    
    def run_production_check(self) -> bool:
        """Run production environment checks."""
        print(f"\n{'='*60}")
        print("🏭 PRODUCTION ENVIRONMENT CHECKS")
        print(f"{'='*60}")
        
        checks = [
            ("Environment Variables", self._check_environment_variables),
            ("Database Connection", self._check_database_connection),
            ("Import Validation", self._check_import_validation),
            ("Credential Loading", self._check_credential_loading),
            ("NetSuite Connectivity", self._check_netsuite_connectivity)
        ]
        
        all_passed = True
        
        for check_name, check_func in checks:
            print(f"\n🔍 {check_name}...")
            try:
                result = check_func()
                if result:
                    print(f"✅ {check_name}: PASSED")
                    self.results["production"]["passed"] += 1
                else:
                    print(f"❌ {check_name}: FAILED")
                    self.results["production"]["failed"] += 1
                    all_passed = False
            except Exception as e:
                print(f"❌ {check_name}: ERROR - {e}")
                self.results["production"]["errors"].append({
                    "check": check_name,
                    "error": str(e)
                })
                all_passed = False
        
        return all_passed
    
    def _check_environment_variables(self) -> bool:
        """Check required environment variables."""
        required_vars = [
            "IONQ_NETSUITE_ACCOUNT_ID",
            "IONQ_NETSUITE_CONSUMER_KEY",
            "IONQ_NETSUITE_CONSUMER_SECRET",
            "IONQ_NETSUITE_TOKEN_ID",
            "IONQ_NETSUITE_TOKEN_SECRET"
        ]
        
        missing = []
        for var in required_vars:
            if not os.getenv(var):
                missing.append(var)
        
        if missing:
            print(f"  Missing environment variables: {missing}")
            return False
        
        print("  All required environment variables present")
        return True
    
    def _check_database_connection(self) -> bool:
        """Check database connectivity."""
        try:
            # Import Django and try to make a simple query
            import django
            from django.conf import settings
            from django.db import connection
            
            # Test database connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            print("  Database connection successful")
            return True
        except Exception as e:
            print(f"  Database connection failed: {e}")
            return False
    
    def _check_import_validation(self) -> bool:
        """Check that all required imports work."""
        try:
            # Test critical imports
            from didero.workflows.core.activities.erp_sync import sync_erp_purchase_order
            from didero.tasks.actions import confirm_shipment
            from didero.integrations.erp.credentials import SimpleEnvCredentialProvider
            from didero.integrations.erp.clients.netsuite import NetSuiteClient
            from didero.orders.models import PurchaseOrder, Shipment, OrderAcknowledgement
            
            print("  All critical imports successful")
            return True
        except ImportError as e:
            print(f"  Import error: {e}")
            return False
    
    def _check_credential_loading(self) -> bool:
        """Check credential loading functionality."""
        try:
            from didero.integrations.erp.credentials import SimpleEnvCredentialProvider
            
            provider = SimpleEnvCredentialProvider()
            credentials = provider.get_credentials("173", "netsuite")
            
            if not credentials:
                print("  No credentials loaded")
                return False
            
            required_fields = ["account_id", "consumer_key", "consumer_secret", "token_id", "token_secret"]
            missing_fields = [f for f in required_fields if f not in credentials]
            
            if missing_fields:
                print(f"  Missing credential fields: {missing_fields}")
                return False
            
            print("  Credential loading successful")
            return True
        except Exception as e:
            print(f"  Credential loading error: {e}")
            return False
    
    def _check_netsuite_connectivity(self) -> bool:
        """Check NetSuite connectivity."""
        try:
            from didero.integrations.erp.credentials import SimpleEnvCredentialProvider
            from didero.integrations.erp.clients.netsuite import NetSuiteClient
            
            provider = SimpleEnvCredentialProvider()
            credentials = provider.get_credentials("173", "netsuite")
            
            if not credentials:
                print("  No credentials available for connectivity test")
                return False
            
            client = NetSuiteClient(credentials)
            
            # Test connection (this will make actual API call)
            # For safety, we'll just test client creation
            print("  NetSuite client created successfully")
            print(f"  Endpoint: {client.endpoint}")
            
            return True
        except Exception as e:
            print(f"  NetSuite connectivity error: {e}")
            return False
    
    def run_specific_test(self, test_name: str) -> bool:
        """Run a specific test by name."""
        print(f"\n{'='*60}")
        print(f"🎯 RUNNING SPECIFIC TEST: {test_name}")
        print(f"{'='*60}")
        
        # Find which file contains the test
        test_files = [
            "test_manual_erp_flow_unit.py",
            "test_manual_erp_flow_integration.py", 
            "test_manual_erp_flow_e2e.py",
            "test_manual_erp_flow_production.py"
        ]
        
        for test_file in test_files:
            layer = test_file.replace("test_manual_erp_flow_", "").replace(".py", "")
            cmd = [
                "python", "manage.py", "test",
                f"didero.tasks.tests.test_manual_erp_flow_{layer}.TestManualERPFlow{layer.capitalize()}.{test_name}",
                "--verbosity=2"
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd="/Users/<USER>/Desktop/work/main/didero-api"
            )
            
            if "1 passed" in result.stdout or "PASSED" in result.stdout:
                print(f"✅ Test {test_name} PASSED in {test_file}")
                print(result.stdout)
                return True
            elif "1 failed" in result.stdout or "FAILED" in result.stdout:
                print(f"❌ Test {test_name} FAILED in {test_file}")
                print(result.stdout)
                print(result.stderr)
                return False
        
        print(f"❓ Test {test_name} not found in any test file")
        return False
    
    def generate_report(self):
        """Generate comprehensive test report."""
        duration = datetime.now() - self.start_time
        
        print(f"\n{'='*80}")
        print("📊 COMPREHENSIVE ERP TEST REPORT")
        print(f"{'='*80}")
        print(f"Test Duration: {duration}")
        print(f"Timestamp: {datetime.now()}")
        
        total_passed = sum(layer["passed"] for layer in self.results.values())
        total_failed = sum(layer["failed"] for layer in self.results.values())
        total_errors = sum(len(layer["errors"]) for layer in self.results.values())
        
        print(f"\n📈 OVERALL SUMMARY:")
        print(f"  ✅ Passed: {total_passed}")
        print(f"  ❌ Failed: {total_failed}")
        print(f"  🚨 Errors: {total_errors}")
        print(f"  📊 Success Rate: {total_passed/(total_passed+total_failed)*100:.1f}%" if (total_passed+total_failed) > 0 else "  📊 Success Rate: N/A")
        
        for layer, results in self.results.items():
            if results["passed"] > 0 or results["failed"] > 0 or results["errors"]:
                print(f"\n🔹 {layer.upper()} LAYER:")
                print(f"  ✅ Passed: {results['passed']}")
                print(f"  ❌ Failed: {results['failed']}")
                print(f"  🚨 Errors: {len(results['errors'])}")
                
                if results["errors"]:
                    print(f"  📝 Error Details:")
                    for error in results["errors"][:3]:  # Show first 3 errors
                        if isinstance(error, dict):
                            print(f"    • {error.get('test', error.get('check', 'Unknown'))}: {error['error'][:100]}...")
                        else:
                            print(f"    • {str(error)[:100]}...")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if total_failed == 0 and total_errors == 0:
            print("  🎉 All tests passed! System is ready for production.")
        else:
            print("  🔧 Issues found that need attention:")
            
            if self.results["unit"]["failed"] > 0:
                print("    • Fix unit test failures - these indicate core logic issues")
            
            if self.results["integration"]["failed"] > 0:
                print("    • Fix integration test failures - these indicate component interaction issues")
            
            if self.results["e2e"]["failed"] > 0:
                print("    • Fix end-to-end test failures - these indicate workflow issues")
                
            if self.results["production"]["failed"] > 0:
                print("    • Fix production environment issues - these will cause runtime failures")
        
        # Save detailed report
        report_file = f"/tmp/erp_test_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump({
                "timestamp": datetime.now().isoformat(),
                "duration": str(duration),
                "results": self.results,
                "summary": {
                    "total_passed": total_passed,
                    "total_failed": total_failed,
                    "total_errors": total_errors
                }
            }, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")


def main():
    parser = argparse.ArgumentParser(description="Comprehensive ERP test runner")
    parser.add_argument("--layer", choices=["unit", "integration", "e2e", "production"], 
                       help="Run tests for specific layer")
    parser.add_argument("--test", help="Run specific test by name")
    parser.add_argument("--production-check", action="store_true", 
                       help="Run production environment checks")
    parser.add_argument("--all", action="store_true", 
                       help="Run all test layers")
    
    args = parser.parse_args()
    
    runner = ERPTestRunner()
    
    try:
        if args.test:
            success = runner.run_specific_test(args.test)
        elif args.production_check:
            success = runner.run_production_check()
        elif args.layer:
            success = runner.run_layer(args.layer)
        elif args.all:
            success = True
            for layer in ["unit", "integration", "e2e", "production"]:
                layer_success = runner.run_layer(layer)
                success = success and layer_success
            
            # Also run production checks
            prod_success = runner.run_production_check()
            success = success and prod_success
        else:
            # Default: run all layers
            success = True
            for layer in ["unit", "integration", "e2e"]:
                layer_success = runner.run_layer(layer)
                success = success and layer_success
        
        runner.generate_report()
        
        if success:
            print(f"\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
            sys.exit(0)
        else:
            print(f"\n💥 SOME TESTS FAILED - CHECK REPORT ABOVE")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n⏹️  Test execution interrupted by user")
        runner.generate_report()
        sys.exit(130)
    except Exception as e:
        print(f"\n💥 UNEXPECTED ERROR: {e}")
        runner.generate_report()
        sys.exit(1)


if __name__ == "__main__":
    main()