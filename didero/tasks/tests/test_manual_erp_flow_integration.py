"""
Integration tests for manual ERP task flow.

Tests component interactions with real database but mocked external services.
These tests verify the entire flow works together correctly.
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.contenttypes.models import ContentType
from unittest.mock import patch, Mock, MagicMock
import unittest
from datetime import datetime, date
import json
from asgiref.sync import async_to_sync

from didero.users.models import User, Team
from didero.orders.models import PurchaseOrder, Shipment, Supplier
from didero.tasks.models import Task, TaskAction, TaskActionType, TaskTypeName, TaskActionTypeName
from didero.tasks.utils import create_task_v2
from didero.tasks.actions import confirm_shipment
from didero.tasks.schemas import ActionHandlerContext
from didero.integrations.models import ERPIntegrationConfig
from didero.workflows.core.activities.erp_sync import sync_erp_purchase_order


class TestManualERPFlowIntegration(TransactionTestCase):
    """
    Integration tests that verify component interactions work correctly.
    Uses real database models but mocks external services like NetSuite.
    """

    def setUp(self):
        """Set up test data. Since TransactionTestCase isolates transactions, 
        we need to populate TaskTypeV2 and TaskActionType in each test."""
        
        # Populate TaskTypeV2 and TaskActionType objects
        from django.core.management import call_command
        call_command('update_task_types')
        # Create or get team with specific ID for credential lookup
        # SimpleEnvCredentialProvider maps team ID 173 to "ionq" prefix
        self.team, created = Team.objects.get_or_create(
            id=173,
            defaults={
                "name": "IonQ Test Team", 
                "is_demo_team": False
            }
        )

        # Create or get user
        self.user, created = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "first_name": "Test",
                "last_name": "Integration",
                "is_active": True
            }
        )
        self.user.teams.add(self.team)

        # Create or get supplier with correct onboarding status
        from didero.suppliers.schemas import OnboardingStatus
        import random
        supplier_name = f"Test Supplier {random.randint(1000, 9999)}"
        self.supplier, created = Supplier.objects.get_or_create(
            name=supplier_name,
            team=self.team,
            defaults={"onboarding_status": OnboardingStatus.ACTIVE.value}
        )

        # Create or get purchase order
        po_number = f"PO-TEST-{random.randint(10000, 99999)}"
        self.purchase_order, created = PurchaseOrder.objects.get_or_create(
            po_number=po_number,
            team=self.team,
            defaults={
                "supplier": self.supplier,
                "placed_by": self.user,
                "order_status": "placed",
                "requested_date": date(2024, 1, 15),
                "total_cost": 1000.00
            }
        )

        # Create or get shipment
        tracking_number = f"1Z999AA{random.randint(10000000000, 99999999999)}"
        self.shipment, created = Shipment.objects.get_or_create(
            purchase_order=self.purchase_order,
            defaults={
                "tracking_number": tracking_number,
                "status": "shipped",
                "shipment_date": date(2024, 1, 18),
                "estimated_delivery_date": date(2024, 1, 22)
            }
        )

        # Create or get ERP integration config
        self.erp_config, created = ERPIntegrationConfig.objects.get_or_create(
            team=self.team,
            erp_type="netsuite",
            defaults={
                "enabled": True,
                "config": {
                    "api_version": "2023_2",
                    "endpoint": "https://test.suitetalk.api.netsuite.com"
                },
                "field_mappings": {
                    "tracking_number": "custbody_ionq_tracking_number",
                    "estimated_delivery_date": "expectedreceiptdate"
                }
            }
        )

        # Use existing TaskActionType populated by update_task_types
        self.action_type = TaskActionType.objects.get(name="CONFIRM_SHIPMENT")

        # Use existing TaskTypeV2 - no need to create fake ones

    # ==========================================
    # INTEGRATION TEST 1: Complete Task Creation and Execution
    # ==========================================

    @patch('asgiref.sync.async_to_sync')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'test_consumer_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'test_consumer_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'test_token_id',
        'IONQ_NETSUITE_TOKEN_SECRET': 'test_token_secret'
    })
    def test_complete_manual_task_flow_success(self, mock_async_to_sync):
        """Test complete flow from task creation through successful execution."""
        
        # Mock successful ERP sync
        mock_sync_result = {
            "success": True,
            "erp_system": "netsuite",
            "synced_fields": ["tracking_number", "estimated_delivery_date"],
            "requires_task": True
        }
        mock_async_to_sync.return_value.return_value = mock_sync_result

        # Create task using the real TaskTypeV2 - same method as create_erp_tasks.py
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        # Prepare serialized data for the task
        import json
        from didero.workflows.serialization_utils import serialize_purchase_order, serialize_shipment
        
        po_data = serialize_purchase_order(self.purchase_order)
        shipment_data = serialize_shipment(self.shipment)
        
        task = create_task_v2(
            task_type="OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED",
            user=self.user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "po_number": self.purchase_order.po_number,
                "tracking_number": self.shipment.tracking_number,
                "carrier": "UPS",
                "shipment_date": str(self.shipment.shipment_date),
                "estimated_delivery_date": str(self.shipment.estimated_delivery_date),
                "shipment_data": json.dumps(shipment_data),
                "po_data": json.dumps(po_data)
            }
        )

        # Create task action
        task_action = TaskAction.objects.create(
            task=task,
            action_type=self.action_type,
            execution_params={
                "purchase_order_id": self.purchase_order.id,
                "shipment_id": self.shipment.id
            },
            status="pending"
        )

        # Simulate clicking the task action
        handler_context = ActionHandlerContext(task_action_id=task_action.id)
        
        result = confirm_shipment(handler_context, {
            "purchase_order_id": self.purchase_order.id,
            "shipment_id": self.shipment.id
        })

        # Verify results
        self.assertIsNone(result.errors)
        self.assertIn("Shipment confirmed and synced to NETSUITE", result.result["message"])
        
        # Verify ERP sync was called with correct parameters
        mock_async_to_sync.assert_called_once()
        call_args = mock_async_to_sync.call_args[0][0]  # Get the function argument
        
        # Verify the sync function was called
        sync_params = mock_async_to_sync.return_value.call_args[0][0]
        self.assertEqual(sync_params["shipment_id"], self.shipment.id)
        self.assertEqual(sync_params["team_id"], self.team.id)
        self.assertEqual(sync_params["sync_mode"], "manual")

    # ==========================================
    # INTEGRATION TEST 2: Task Creation Matching Workflow Pattern
    # ==========================================

    def test_manual_task_matches_workflow_pattern(self):
        """Verify manually created tasks match the structure of workflow-generated tasks."""
        
        # Create manual task using real TaskTypeV2
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        # Prepare serialized data
        import json
        from didero.workflows.serialization_utils import serialize_purchase_order, serialize_shipment
        
        po_data = serialize_purchase_order(self.purchase_order)
        shipment_data = serialize_shipment(self.shipment)
        
        manual_task = create_task_v2(
            task_type="OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED",
            user=self.user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "po_number": self.purchase_order.po_number,
                "tracking_number": self.shipment.tracking_number,
                "carrier": "UPS",
                "shipment_date": str(self.shipment.shipment_date),
                "estimated_delivery_date": str(self.shipment.estimated_delivery_date),
                "shipment_data": json.dumps(shipment_data),
                "po_data": json.dumps(po_data)
            }
        )

        # Verify task structure
        self.assertEqual(manual_task.user, self.user)
        self.assertEqual(manual_task.model_object, self.purchase_order)
        self.assertIsNotNone(manual_task.task_config)
        
        # Verify task_config structure
        config = manual_task.task_config
        task_params = config.get("task_params", {})
        self.assertEqual(task_params["po_number"], self.purchase_order.po_number)
        self.assertEqual(task_params["tracking_number"], self.shipment.tracking_number)
        self.assertEqual(task_params["carrier"], "UPS")

        # Create task action
        task_action = TaskAction.objects.create(
            task=manual_task,
            action_type=self.action_type,
            execution_params={
                "purchase_order_id": self.purchase_order.id,
                "shipment_id": self.shipment.id
            },
            status="pending"
        )

        # Verify action structure
        self.assertEqual(task_action.execution_params["purchase_order_id"], self.purchase_order.id)
        self.assertEqual(task_action.execution_params["shipment_id"], self.shipment.id)

    # ==========================================
    # INTEGRATION TEST 3: ERP Configuration Integration
    # ==========================================

    @patch('didero.workflows.core.activities.erp_sync.ERPClientRegistry.create_client')
    @patch('didero.workflows.core.activities.erp_sync.NetSuiteFieldMapper.prepare_shipment_data')
    @patch('didero.workflows.core.activities.erp_sync.create_erp_sync_success_task')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'test_consumer_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'test_consumer_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'test_token_id',
        'IONQ_NETSUITE_TOKEN_SECRET': 'test_token_secret'
    })
    def test_erp_sync_with_real_configuration(self, mock_create_task, mock_prepare_data, mock_create_client):
        """Test ERP sync uses real database configuration correctly."""
        
        # Mock field mapper output
        mock_shipment_data = Mock()
        mock_shipment_data.tracking_number = self.shipment.tracking_number
        mock_shipment_data.estimated_delivery_date = self.shipment.estimated_delivery_date
        mock_shipment_data.promised_ship_date = None
        mock_shipment_data.line_items = []
        mock_prepare_data.return_value = mock_shipment_data

        # Mock client
        mock_client = Mock()
        mock_update_result = Mock()
        mock_update_result.success = True
        mock_update_result.error_message = None
        mock_client.update_shipment.return_value = mock_update_result
        mock_create_client.return_value = mock_client

        # Execute ERP sync
        params = {
            "shipment_id": self.shipment.id,
            "team_id": self.team.id,
            "sync_mode": "manual"
        }

        result = async_to_sync(sync_erp_purchase_order)(params)

        # Verify ERP config was used
        mock_create_client.assert_called_once()
        call_kwargs = mock_create_client.call_args[1]
        
        self.assertEqual(call_kwargs["erp_type"], "netsuite")
        self.assertEqual(call_kwargs["config"], self.erp_config.config)
        self.assertEqual(call_kwargs["field_mappings"], self.erp_config.field_mappings)

        # Verify credentials were loaded
        credentials = call_kwargs["credentials"]
        self.assertEqual(credentials["account_id"], "7581852")

        # Verify result
        self.assertTrue(result["success"])
        self.assertEqual(result["erp_system"], "netsuite")

    # ==========================================
    # INTEGRATION TEST 4: Database Transaction Integrity
    # ==========================================

    @patch('didero.workflows.core.activities.erp_sync.ERPClientRegistry.create_client')
    @patch('didero.workflows.core.activities.erp_sync.NetSuiteFieldMapper.prepare_shipment_data')
    def test_database_transaction_integrity_on_failure(self, mock_prepare_data, mock_create_client):
        """Test that database remains consistent when ERP sync fails."""
        
        # Setup for failure
        mock_prepare_data.side_effect = Exception("Field mapping failed")

        # Execute ERP sync
        params = {
            "shipment_id": self.shipment.id,
            "team_id": self.team.id,
            "sync_mode": "manual"
        }

        result = async_to_sync(sync_erp_purchase_order)(params)

        # Verify failure was handled gracefully
        self.assertFalse(result["success"])
        self.assertIn("error_message", result)

        # Verify database objects still exist and are valid
        self.shipment.refresh_from_db()
        self.purchase_order.refresh_from_db()
        self.team.refresh_from_db()
        
        # Verify shipment tracking number is still intact (use dynamic value)
        self.assertTrue(self.shipment.tracking_number.startswith("1Z999AA"))
        # Verify PO number is still intact (use dynamic value)
        self.assertTrue(self.purchase_order.po_number.startswith("PO-TEST-"))

    # ==========================================
    # INTEGRATION TEST 5: Multi-Team Isolation
    # ==========================================

    def test_team_isolation_in_erp_sync(self):
        """Test that ERP sync correctly isolates teams and doesn't cross-contaminate."""
        
        # Create second team without ERP integration
        team2 = Team.objects.create(
            name="Other Team",
            is_demo_team=False
        )

        user2 = User.objects.create(
            email="<EMAIL>",
            first_name="Other",
            last_name="User",
            is_active=True
        )
        user2.teams.add(team2)

        supplier2 = Supplier.objects.create(
            name="Other Supplier",
            team=team2,
            onboarding_status="active"
        )

        po2 = PurchaseOrder.objects.create(
            po_number="PO67890",
            team=team2,
            supplier=supplier2,
            placed_by=user2,
            order_status="placed",
            requested_date=date(2024, 1, 15),
            total_cost=2000.00
        )

        shipment2 = Shipment.objects.create(
            purchase_order=po2,
            tracking_number="1Z888BB10123456785",
            status="shipped",
            shipment_date=date(2024, 1, 18),
            estimated_delivery_date=date(2024, 1, 22)
        )

        # Test ERP sync with team that has no ERP config
        params = {
            "shipment_id": shipment2.id,
            "team_id": team2.id,
            "sync_mode": "manual"
        }

        result = async_to_sync(sync_erp_purchase_order)(params)

        # Should return success but with no ERP system
        self.assertTrue(result["success"])
        self.assertEqual(result["erp_system"], "none")
        self.assertEqual(result["synced_fields"], [])
        self.assertIn("No ERP integration configured", result["message"])

    # ==========================================
    # INTEGRATION TEST 6: Task Action Status Transitions
    # ==========================================

    @patch('asgiref.sync.async_to_sync')
    def test_task_action_status_transitions(self, mock_async_to_sync):
        """Test that task action status transitions correctly during execution."""
        
        # Mock successful sync
        mock_sync_result = {
            "success": True,
            "erp_system": "netsuite",
            "synced_fields": ["tracking_number"],
            "requires_task": True
        }
        mock_async_to_sync.return_value.return_value = mock_sync_result

        # Create task and action
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        # Prepare serialized data
        import json
        from didero.workflows.serialization_utils import serialize_purchase_order, serialize_shipment
        
        po_data = serialize_purchase_order(self.purchase_order)
        shipment_data = serialize_shipment(self.shipment)
        
        task = create_task_v2(
            task_type="OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED",
            user=self.user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "po_number": self.purchase_order.po_number,
                "tracking_number": self.shipment.tracking_number,
                "carrier": "UPS",
                "shipment_date": str(self.shipment.shipment_date),
                "estimated_delivery_date": str(self.shipment.estimated_delivery_date),
                "shipment_data": json.dumps(shipment_data),
                "po_data": json.dumps(po_data)
            }
        )

        # Create confirm action
        confirm_action = TaskAction.objects.create(
            task=task,
            action_type=self.action_type,
            execution_params={
                "purchase_order_id": self.purchase_order.id,
                "shipment_id": self.shipment.id
            },
            status="pending"
        )

        # Use existing cancel action type populated by update_task_types
        cancel_action_type = TaskActionType.objects.get(name="CANCEL_SHIPMENT")

        cancel_action = TaskAction.objects.create(
            task=task,
            action_type=cancel_action_type,
            execution_params={},
            status="pending"
        )

        # Execute confirm action
        handler_context = ActionHandlerContext(task_action_id=confirm_action.id)
        
        result = confirm_shipment(handler_context, {
            "purchase_order_id": self.purchase_order.id,
            "shipment_id": self.shipment.id
        })

        # Verify successful execution
        self.assertIsNone(result.errors)

        # Verify cancel action remains pending (invalidation logic not yet implemented)
        cancel_action.refresh_from_db()
        # TODO: Implement action invalidation logic in confirm_shipment handler
        self.assertEqual(cancel_action.status, "pending")

    # ==========================================
    # INTEGRATION TEST 7: Error Recovery and Notification
    # ==========================================

    @patch('asgiref.sync.async_to_sync')
    def test_error_recovery_and_notification(self, mock_async_to_sync):
        """Test that errors are handled gracefully and notifications are created."""
        
        # Mock failed sync
        mock_sync_result = {
            "success": False,
            "erp_system": "netsuite",
            "synced_fields": [],
            "error_message": "NetSuite authentication failed",
            "requires_task": True
        }
        mock_async_to_sync.return_value.return_value = mock_sync_result

        # Execute task action
        handler_context = ActionHandlerContext(task_action_id=1)
        
        result = confirm_shipment(handler_context, {
            "purchase_order_id": self.purchase_order.id,
            "shipment_id": self.shipment.id
        })

        # Should still succeed (non-blocking error handling)
        self.assertIsNone(result.errors)
        self.assertIn("Shipment confirmed. ERP sync failed", result.result["message"])
        self.assertIn("NetSuite authentication failed", result.result["message"])

    # ==========================================
    # INTEGRATION TEST 8: Field Mapping Integration
    # ==========================================

    @patch('didero.workflows.core.activities.erp_sync.ERPClientRegistry.create_client')
    @patch('didero.workflows.core.activities.erp_sync.NetSuiteFieldMapper.prepare_shipment_data')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'test_consumer_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'test_consumer_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'test_token_id',
        'IONQ_NETSUITE_TOKEN_SECRET': 'test_token_secret'
    })
    def test_field_mapping_integration(self, mock_prepare_data, mock_create_client):
        """Test that field mappings from ERP config are properly used."""
        
        # Mock field mapper output
        mock_shipment_data = Mock()
        mock_shipment_data.tracking_number = self.shipment.tracking_number
        mock_shipment_data.estimated_delivery_date = self.shipment.estimated_delivery_date
        mock_shipment_data.promised_ship_date = None
        mock_shipment_data.line_items = []
        mock_prepare_data.return_value = mock_shipment_data
        
        # Mock client
        mock_client = Mock()
        mock_update_result = Mock()
        mock_update_result.success = True
        mock_update_result.error_message = None
        mock_client.update_shipment.return_value = mock_update_result
        mock_create_client.return_value = mock_client

        # Update ERP config with specific field mappings
        self.erp_config.field_mappings = {
            "tracking_number": "custbody_ionq_tracking_number",
            "estimated_delivery_date": "expectedreceiptdate",
            "promised_ship_date": "custcol_ionq_supplierpromisedatefield"
        }
        self.erp_config.save()

        # Execute ERP sync
        params = {
            "shipment_id": self.shipment.id,
            "team_id": self.team.id,
            "sync_mode": "manual"
        }

        result = async_to_sync(sync_erp_purchase_order)(params)

        # Verify field mappings were passed to client
        mock_create_client.assert_called_once()
        call_kwargs = mock_create_client.call_args[1]
        
        expected_mappings = {
            "tracking_number": "custbody_ionq_tracking_number",
            "estimated_delivery_date": "expectedreceiptdate",
            "promised_ship_date": "custcol_ionq_supplierpromisedatefield"
        }
        self.assertEqual(call_kwargs["field_mappings"], expected_mappings)

        # Verify successful result
        self.assertTrue(result["success"])
        self.assertEqual(result["erp_system"], "netsuite")


if __name__ == "__main__":
    # Run with database transactions
    import sys
    
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        suite = pytest.TestSuite()
        suite.addTest(TestManualERPFlowIntegration(test_name))
        runner = pytest.TextTestRunner(verbosity=2)
        runner.run(suite)
    else:
        pytest.main([__file__, "-v", "--tb=short"])