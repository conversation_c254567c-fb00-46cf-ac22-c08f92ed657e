"""
Production simulation tests for manual ERP task flow.

Tests against real production data patterns and validates production readiness.
These tests simulate actual production scenarios with real PO numbers and data structures.
"""

import pytest
from django.test import TestCase, TransactionTestCase
from django.core.management import call_command
from unittest.mock import patch, Mock
from datetime import datetime, date
import json
import os
from asgiref.sync import async_to_sync

from didero.tasks.management.commands.create_erp_tasks import Command as CreateERPTasksCommand
from didero.tasks.management.commands.debug_erp_end_to_end import Command as DebugERPCommand
from didero.tasks.actions import confirm_shipment
from didero.tasks.schemas import ActionHandlerContext
from didero.workflows.core.activities.erp_sync import sync_erp_purchase_order
from didero.integrations.erp.credentials import SimpleEnvCredentialProvider
from didero.integrations.erp.clients.netsuite import NetSuiteClient


class TestManualERPFlowProduction(TestCase):
    """
    Production simulation tests using real production patterns.
    These tests validate the system works with actual production data structures.
    """

    def setUp(self):
        """Set up production-like environment."""
        self.production_po_numbers = ["PO11410", "PO12302", "PO12345"]
        self.ionq_team_id = 173
        self.test_team_id = 4

    # ==========================================
    # PRODUCTION TEST 1: Environment Variable Loading
    # ==========================================

    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': '****************************************************************',
        'IONQ_NETSUITE_CONSUMER_SECRET': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_ID': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_SECRET': '****************************************************************'
    })
    def test_production_environment_variable_loading(self):
        """Test that production environment variables load correctly."""
        
        provider = SimpleEnvCredentialProvider()
        
        # Test IonQ production team (173)
        ionq_credentials = provider.get_credentials(str(self.ionq_team_id), "netsuite")
        
        self.assertEqual(ionq_credentials["account_id"], "7581852")
        self.assertEqual(len(ionq_credentials["consumer_key"]), 64)  # Expected length
        self.assertEqual(len(ionq_credentials["consumer_secret"]), 64)
        self.assertEqual(len(ionq_credentials["token_id"]), 64)
        self.assertEqual(len(ionq_credentials["token_secret"]), 64)
        
        # Test IonQ test team (4)  
        test_credentials = provider.get_credentials(str(self.test_team_id), "netsuite")
        
        # Should use same IONQ_* env vars
        self.assertEqual(test_credentials["account_id"], "7581852")
        self.assertEqual(test_credentials["consumer_key"], ionq_credentials["consumer_key"])

        # Validate credentials
        self.assertTrue(provider.validate_credentials(ionq_credentials, "netsuite"))

    # ==========================================
    # PRODUCTION TEST 2: NetSuite Client Production Initialization
    # ==========================================

    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': '****************************************************************',
        'IONQ_NETSUITE_CONSUMER_SECRET': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_ID': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_SECRET': '****************************************************************'
    })
    def test_netsuite_client_production_initialization(self):
        """Test NetSuite client can be initialized with production credentials."""
        
        provider = SimpleEnvCredentialProvider()
        credentials = provider.get_credentials(str(self.ionq_team_id), "netsuite")
        
        # Should not raise exception
        client = NetSuiteClient(credentials)
        
        # Verify client configuration
        self.assertEqual(client.credentials["account_id"], "7581852")
        self.assertEqual(client.api_version, "2023_2")
        self.assertEqual(client.endpoint, "https://7581852.suitetalk.api.netsuite.com/services/NetSuitePort_2023_2")

    # ==========================================
    # PRODUCTION TEST 3: OAuth Signature Production Validation
    # ==========================================

    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': '****************************************************************',
        'IONQ_NETSUITE_CONSUMER_SECRET': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_ID': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_SECRET': '****************************************************************'
    })
    def test_oauth_signature_production_validation(self):
        """Test OAuth signature generation with production credentials."""
        
        provider = SimpleEnvCredentialProvider()
        credentials = provider.get_credentials(str(self.ionq_team_id), "netsuite")
        
        client = NetSuiteClient(credentials)
        
        # Generate multiple signatures
        signatures = []
        for _ in range(5):
            timestamp, nonce, signature = client._generate_oauth_signature()
            
            # Verify signature components
            self.assertIsInstance(timestamp, str)
            self.assertEqual(len(nonce), 20)
            self.assertTrue(len(signature) > 20)  # Base64 encoded should be substantial
            
            # Verify uniqueness (nonce should be different each time)
            signatures.append((timestamp, nonce, signature))

        # Verify all nonces are unique
        nonces = [sig[1] for sig in signatures]
        self.assertEqual(len(nonces), len(set(nonces)))

    # ==========================================
    # PRODUCTION TEST 4: SOAP Envelope Production Structure
    # ==========================================

    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': '****************************************************************',
        'IONQ_NETSUITE_CONSUMER_SECRET': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_ID': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_SECRET': '****************************************************************'
    })
    def test_soap_envelope_production_structure(self):
        """Test SOAP envelope contains all required production elements."""
        
        provider = SimpleEnvCredentialProvider()
        credentials = provider.get_credentials(str(self.ionq_team_id), "netsuite")
        
        client = NetSuiteClient(credentials)
        
        # Test with realistic body content
        body_content = '''
            <platformMsgs:get>
                <platformMsgs:baseRef xsi:type="platformCore:RecordRef" type="purchaseOrder" internalId="12345">
                </platformMsgs:baseRef>
            </platformMsgs:get>
        '''
        
        envelope = client._create_soap_envelope(body_content)
        
        # Verify required namespaces
        required_namespaces = [
            'xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"',
            'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"',
            'xmlns:platformCore="urn:core_2023_2.platform.webservices.netsuite.com"',
            'xmlns:platformMsgs="urn:messages_2023_2.platform.webservices.netsuite.com"',
            'xmlns:tranPurch="urn:purchases_2023_2.transactions.webservices.netsuite.com"'
        ]
        
        for namespace in required_namespaces:
            self.assertIn(namespace, envelope)

        # Verify authentication elements
        auth_elements = [
            '<platformCore:account>7581852</platformCore:account>',
            '<platformCore:consumerKey>****************************************************************</platformCore:consumerKey>',
            '<platformCore:token>****************************************************************</platformCore:token>',
            '<platformCore:signature algorithm="HMAC-SHA256">'
        ]
        
        for element in auth_elements:
            self.assertIn(element, envelope)

        # Verify body content is included
        self.assertIn('type="purchaseOrder" internalId="12345"', envelope)

    # ==========================================
    # PRODUCTION TEST 5: Management Command Production Simulation
    # ==========================================

    @patch('didero.tasks.management.commands.create_erp_tasks.Command.handle')
    def test_create_erp_tasks_command_production_simulation(self, mock_handle):
        """Test create_erp_tasks command with production-like arguments."""
        
        mock_handle.return_value = None
        
        # Test command with production PO number
        command = CreateERPTasksCommand()
        
        # Simulate different command scenarios
        test_scenarios = [
            {
                "po_number": "PO11410",
                "team_id": 173,
                "task_type": "shipment"
            },
            {
                "po_number": "PO12302", 
                "team_id": 173,
                "task_type": "oa"
            },
            {
                "po_number": "PO12345",
                "team_id": 173,
                "task_type": "both"
            }
        ]
        
        for scenario in test_scenarios:
            # Verify command can be called without errors
            try:
                # This would normally call the real command
                # We're testing the command structure is valid
                mock_handle.assert_not_called()  # Reset
                
                # Simulate calling with args
                call_command(
                    'create_erp_tasks',
                    po_number=scenario["po_number"],
                    team_id=scenario["team_id"],
                    task_type=scenario["task_type"]
                )
                
            except Exception as e:
                self.fail(f"Command failed with scenario {scenario}: {e}")

    # ==========================================
    # PRODUCTION TEST 6: Debug Command Production Readiness
    # ==========================================

    @patch('didero.tasks.management.commands.debug_erp_end_to_end.Command.handle')
    def test_debug_command_production_readiness(self, mock_handle):
        """Test debug command can handle production scenarios."""
        
        mock_handle.return_value = None
        
        # Test comprehensive test mode
        try:
            call_command('debug_erp_end_to_end', comprehensive_test=True)
        except Exception as e:
            self.fail(f"Comprehensive test command failed: {e}")

        # Test specific PO mode
        try:
            call_command(
                'debug_erp_end_to_end',
                po_number="PO11410",
                team_id=173
            )
        except Exception as e:
            self.fail(f"Specific PO test command failed: {e}")

        # Test NetSuite connection only
        try:
            call_command('debug_erp_end_to_end', test_netsuite_connection=True)
        except Exception as e:
            self.fail(f"NetSuite connection test command failed: {e}")

    # ==========================================
    # PRODUCTION TEST 7: Production Data Structure Validation
    # ==========================================

    def test_production_data_structure_validation(self):
        """Test that our test data structures match production requirements."""
        
        # Test PO number format validation
        po_patterns = [
            "PO11410",   # Standard format
            "PO12302",   # Standard format  
            "PO12345",   # Test format
            "PO00001",   # Edge case
            "PO99999"    # Edge case
        ]
        
        for po_number in po_patterns:
            # Verify PO number format
            self.assertTrue(po_number.startswith("PO"))
            self.assertEqual(len(po_number), 7)
            self.assertTrue(po_number[2:].isdigit())

        # Test tracking number format validation
        tracking_patterns = [
            "1Z999AA10123456784",  # UPS format
            "1Z888BB20123456785",  # UPS format variant
            "9400 1000 0000 0000 0000 00",  # USPS format
            "8000 0000 0000"       # FedEx format
        ]
        
        for tracking in tracking_patterns:
            # Verify tracking number has minimum length
            self.assertTrue(len(tracking.replace(" ", "")) >= 10)

        # Test team ID validation
        valid_team_ids = [4, 173, 1, 999]
        for team_id in valid_team_ids:
            self.assertIsInstance(team_id, int)
            self.assertTrue(team_id > 0)

    # ==========================================
    # PRODUCTION TEST 8: Error Message Production Compatibility
    # ==========================================

    def test_error_message_production_compatibility(self):
        """Test that error messages are production-ready and informative."""
        
        # Test credential validation error messages
        provider = SimpleEnvCredentialProvider()
        
        # Test with empty credentials
        empty_creds = {}
        is_valid = provider.validate_credentials(empty_creds, "netsuite")
        self.assertFalse(is_valid)

        # Test with incomplete credentials
        incomplete_creds = {"account_id": "test"}
        is_valid = provider.validate_credentials(incomplete_creds, "netsuite")
        self.assertFalse(is_valid)

        # Test NetSuite client initialization errors
        with self.assertRaises(ValueError) as context:
            NetSuiteClient({})
        
        error_message = str(context.exception)
        self.assertIn("Missing required credentials", error_message)
        
        # Verify all required fields are mentioned
        required_fields = ["account_id", "consumer_key", "consumer_secret", "token_id", "token_secret"]
        for field in required_fields:
            self.assertIn(field, error_message)

    # ==========================================
    # PRODUCTION TEST 9: Performance and Memory Validation
    # ==========================================

    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': '****************************************************************',
        'IONQ_NETSUITE_CONSUMER_SECRET': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_ID': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_SECRET': '****************************************************************'
    })
    def test_performance_and_memory_validation(self):
        """Test that the system performs adequately under production load."""
        
        import time
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Test credential loading performance
        provider = SimpleEnvCredentialProvider()
        
        start_time = time.time()
        for _ in range(100):
            credentials = provider.get_credentials(str(self.ionq_team_id), "netsuite")
            provider.validate_credentials(credentials, "netsuite")
        credential_time = time.time() - start_time
        
        # Should be fast (under 1 second for 100 iterations)
        self.assertLess(credential_time, 1.0, "Credential loading too slow")

        # Test NetSuite client initialization performance
        start_time = time.time()
        for _ in range(10):
            client = NetSuiteClient(credentials)
            timestamp, nonce, signature = client._generate_oauth_signature()
        client_time = time.time() - start_time
        
        # Should be reasonable (under 1 second for 10 iterations)
        self.assertLess(client_time, 1.0, "Client initialization too slow")

        # Check memory usage hasn't exploded
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Should not use excessive memory (less than 50MB increase)
        self.assertLess(memory_increase, 50, f"Memory usage increased by {memory_increase}MB")

    # ==========================================
    # PRODUCTION TEST 10: Production Configuration Validation
    # ==========================================

    def test_production_configuration_validation(self):
        """Test that production configuration requirements are met."""
        
        # Test environment variable naming conventions
        expected_env_vars = [
            "IONQ_NETSUITE_ACCOUNT_ID",
            "IONQ_NETSUITE_CONSUMER_KEY",
            "IONQ_NETSUITE_CONSUMER_SECRET", 
            "IONQ_NETSUITE_TOKEN_ID",
            "IONQ_NETSUITE_TOKEN_SECRET"
        ]
        
        for env_var in expected_env_vars:
            # Verify naming convention
            self.assertTrue(env_var.startswith("IONQ_NETSUITE_"))
            self.assertTrue(env_var.endswith(("_ID", "_KEY", "_SECRET")))
            self.assertNotIn("test", env_var.lower())
            self.assertNotIn("dev", env_var.lower())

        # Test API version format
        api_version = "2023_2"
        self.assertRegex(api_version, r"^\d{4}_\d+$")

        # Test endpoint URL format
        account_id = "7581852"
        expected_endpoint = f"https://{account_id}.suitetalk.api.netsuite.com/services/NetSuitePort_{api_version}"
        
        self.assertTrue(expected_endpoint.startswith("https://"))
        self.assertIn("suitetalk.api.netsuite.com", expected_endpoint)
        self.assertIn("NetSuitePort_", expected_endpoint)

        # Test field mapping format
        sample_field_mappings = {
            "tracking_number": "custbody_ionq_tracking_number",
            "estimated_delivery_date": "expectedreceiptdate",
            "promised_ship_date": "custcol_ionq_supplierpromisedatefield"
        }
        
        for internal_field, netsuite_field in sample_field_mappings.items():
            # Verify internal field naming
            self.assertTrue(internal_field.islower())
            self.assertNotIn(" ", internal_field)
            
            # Verify NetSuite field format
            if netsuite_field.startswith("custbody_") or netsuite_field.startswith("custcol_"):
                self.assertIn("ionq", netsuite_field)


class TestManualERPFlowProductionImportError(TestCase):
    """
    Test the specific import error scenario that occurs in production.
    This validates the exact issue you identified.
    """

    # ==========================================
    # PRODUCTION IMPORT ERROR TEST
    # ==========================================

    def test_production_import_error_simulation(self):
        """Test import error that occurs when PurchaseOrder import is missing."""
        
        # Simulate the import error by testing module loading
        import importlib
        import sys
        
        # Test that the import works when present
        try:
            from didero.orders.models import PurchaseOrder, OrderAcknowledgement, Shipment
            self.assertTrue(True, "All imports successful")
        except ImportError as e:
            self.fail(f"Import error occurred: {e}")

        # Test module reloading (simulates deployment)
        if 'didero.workflows.core.activities.erp_sync' in sys.modules:
            try:
                importlib.reload(sys.modules['didero.workflows.core.activities.erp_sync'])
            except Exception as e:
                # This would be the error that kills the process
                if "PurchaseOrder" in str(e) or "NameError" in str(e):
                    self.fail(f"Import error would kill production process: {e}")

    def test_function_signature_validation(self):
        """Test that function signatures requiring PurchaseOrder work correctly."""
        
        from didero.workflows.core.activities.erp_sync import get_erp_notification_recipient_for_po
        from didero.orders.models import PurchaseOrder
        
        # Test that function signature is correct
        import inspect
        
        sig = inspect.signature(get_erp_notification_recipient_for_po)
        params = list(sig.parameters.keys())
        
        self.assertIn('purchase_order', params)
        
        # Test that the parameter annotation works
        purchase_order_param = sig.parameters['purchase_order']
        if purchase_order_param.annotation != inspect.Parameter.empty:
            # If there's a type annotation, it should be PurchaseOrder
            self.assertEqual(purchase_order_param.annotation.__name__, 'PurchaseOrder')


if __name__ == "__main__":
    # Run production simulation tests
    import sys
    
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        suite = pytest.TestSuite()
        suite.addTest(TestManualERPFlowProduction(test_name))
        runner = pytest.TextTestRunner(verbosity=2)
        runner.run(suite)
    else:
        pytest.main([__file__, "-v", "--tb=short", "-x"])