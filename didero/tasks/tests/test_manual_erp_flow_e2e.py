"""
End-to-end tests for manual ERP task flow.

Tests complete flow from task creation through NetSuite API with real production scenarios.
These tests simulate actual user workflows and validate production reliability.
"""

import pytest
from django.test import TestCase, TransactionTestCase
from django.contrib.contenttypes.models import ContentType
from unittest.mock import patch, Mo<PERSON>, MagicMock
from datetime import datetime, date
import json
import requests
from asgiref.sync import async_to_sync

from didero.users.models import User, Team
from didero.orders.models import PurchaseOrder, Shipment, Supplier, Item, OrderItem
from didero.tasks.models import Task, TaskAction, TaskActionType
from didero.tasks.utils import create_task_v2
from didero.tasks.actions import confirm_shipment
from didero.tasks.schemas import ActionHandlerContext
from didero.integrations.models import ERPIntegrationConfig
from didero.workflows.core.activities.erp_sync import sync_erp_purchase_order
from didero.integrations.erp.clients.netsuite import NetSuiteClient


class TestManualERPFlowEndToEnd(TransactionTestCase):
    """
    End-to-end tests that validate complete user workflows.
    These tests simulate real production scenarios with comprehensive data.
    """

    def setUp(self):
        """Create comprehensive test data matching production scenarios."""
        # Create IonQ team (real production data structure)
        self.team = Team.objects.create(
            name="IonQ",
            domain="ionq.com",
            is_demo_team=False
        )

        # Create realistic users
        self.po_creator = User.objects.create(
            email="<EMAIL>",
            first_name="Purchase",
            last_name="Manager",
            is_active=True
        )
        self.po_creator.teams.add(self.team)

        self.ops_user = User.objects.create(
            email="<EMAIL>",
            first_name="Operations",
            last_name="Manager",
            is_active=True
        )
        self.ops_user.teams.add(self.team)

        # Create supplier (matches production suppliers)
        self.supplier = Supplier.objects.create(
            name="ThorLabs Inc",
            team=self.team,
            onboarding_status="active",
            website_url="https://thorlabs.com",
            description="Photonics equipment supplier"
        )

        # Create items
        self.item1 = Item.objects.create(
            item_number="SM600-020",
            description="Single Mode Fiber Optic Patch Cable",
            unit_price=45.50,
            team=self.team
        )

        self.item2 = Item.objects.create(
            item_number="PDA100A2",
            description="Si Switchable Gain Detector",
            unit_price=1250.00,
            team=self.team
        )

        # Create purchase order (realistic PO structure)
        self.purchase_order = PurchaseOrder.objects.create(
            po_number="PO12345",
            team=self.team,
            supplier=self.supplier,
            placed_by=self.po_creator,
            order_status="placed",
            requested_date=date(2024, 1, 15),
            placement_time=datetime(2024, 1, 10, 14, 30),
            total_cost=3250.00
        )

        # Create order items
        OrderItem.objects.create(
            purchase_order=self.purchase_order,
            item=self.item1,
            quantity=10,
            price=45.50
        )

        OrderItem.objects.create(
            purchase_order=self.purchase_order,
            item=self.item2,
            quantity=2,
            price=1250.00
        )

        # Create shipment with realistic tracking
        self.shipment = Shipment.objects.create(
            purchase_order=self.purchase_order,
            tracking_number="1Z999AA10123456784",
            carrier="UPS",
            status="shipped",
            shipment_date=date(2024, 1, 18),
            estimated_delivery_date=date(2024, 1, 22)
        )

        # Create comprehensive ERP integration config
        self.erp_config = ERPIntegrationConfig.objects.create(
            team=self.team,
            erp_type="netsuite",
            enabled=True,
            config={
                "api_version": "2023_2",
                "endpoint": None  # Will use auto-generated endpoint
            },
            field_mappings={
                "tracking_number": "custbody_ionq_tracking_number",
                "estimated_delivery_date": "expectedreceiptdate",
                "promised_ship_date": "custcol_ionq_supplierpromisedatefield"
            }
        )

        # Create task action types
        self.confirm_action_type = TaskActionType.objects.create(
            name="CONFIRM_SHIPMENT",
            description="Confirm shipment and sync to ERP",
            parameters_schema={
                "type": "object",
                "properties": {
                    "purchase_order_id": {"type": "integer"},
                    "shipment_id": {"type": "integer"}
                },
                "required": ["purchase_order_id", "shipment_id"]
            }
        )

        self.cancel_action_type = TaskActionType.objects.create(
            name="CANCEL_SHIPMENT",
            description="Cancel shipment",
            parameters_schema={
                "type": "object",
                "properties": {
                    "purchase_order_id": {"type": "integer"},
                    "shipment_id": {"type": "integer"}
                }
            }
        )

    # ==========================================
    # E2E TEST 1: Complete Production-Like Workflow
    # ==========================================

    @patch('didero.integrations.erp.clients.netsuite.NetSuiteClient._make_soap_request')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': '****************************************************************',
        'IONQ_NETSUITE_CONSUMER_SECRET': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_ID': '****************************************************************',
        'IONQ_NETSUITE_TOKEN_SECRET': '****************************************************************'
    })
    def test_complete_production_workflow(self, mock_soap_request):
        """Test complete workflow from task creation through NetSuite update."""
        
        # Mock NetSuite responses
        search_response = Mock()
        search_response.text = '''
            <searchResult>
                <platformCore:record internalId="12345" type="purchaseOrder">
                    <tranPurch:tranId>PO12345</tranPurch:tranId>
                </platformCore:record>
            </searchResult>
        '''
        search_response.status_code = 200

        update_response = Mock()
        update_response.text = '''
            <platformCore:status isSuccess="true">
                <platformCore:statusDetail>
                    <platformCore:message>Purchase order updated successfully</platformCore:message>
                </platformCore:statusDetail>
            </platformCore:status>
        '''
        update_response.status_code = 200

        mock_soap_request.side_effect = [search_response, update_response]

        # Step 1: Create manual task (simulating create_erp_tasks.py)
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        task = create_task_v2(
            task_type="SHIPMENT_CONFIRMATION",
            user=self.ops_user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "po_number": self.purchase_order.po_number,
                "tracking_number": self.shipment.tracking_number,
                "delivery_date": str(self.shipment.estimated_delivery_date),
                "carrier": self.shipment.carrier,
                "supplier": self.supplier.name
            }
        )

        # Step 2: Create task actions
        confirm_action = TaskAction.objects.create(
            task=task,
            action_type=self.confirm_action_type,
            execution_params={
                "purchase_order_id": self.purchase_order.id,
                "shipment_id": self.shipment.id
            },
            status="pending"
        )

        cancel_action = TaskAction.objects.create(
            task=task,
            action_type=self.cancel_action_type,
            execution_params={
                "purchase_order_id": self.purchase_order.id,
                "shipment_id": self.shipment.id
            },
            status="pending"
        )

        # Step 3: Simulate user clicking "Confirm Shipment"
        handler_context = ActionHandlerContext(task_action_id=confirm_action.id)
        
        result = confirm_shipment(handler_context, {
            "purchase_order_id": self.purchase_order.id,
            "shipment_id": self.shipment.id
        })

        # Step 4: Verify successful execution
        self.assertIsNone(result.errors)
        self.assertIn("Shipment confirmed and synced to NETSUITE", result.result["message"])
        self.assertIn("tracking_number", result.result["message"])

        # Step 5: Verify NetSuite API calls
        self.assertEqual(mock_soap_request.call_count, 2)
        
        # Verify search call
        search_call = mock_soap_request.call_args_list[0]
        self.assertEqual(search_call[1]["action"], "search")
        self.assertIn("PO12345", search_call[1]["body"])

        # Verify update call
        update_call = mock_soap_request.call_args_list[1]
        self.assertEqual(update_call[1]["action"], "update")
        self.assertIn("1Z999AA10123456784", update_call[1]["body"])
        self.assertIn("custbody_ionq_tracking_number", update_call[1]["body"])

        # Step 6: Verify cancel action was invalidated
        cancel_action.refresh_from_db()
        self.assertEqual(cancel_action.status, "invalidated")

        # Step 7: Verify task status
        task.refresh_from_db()
        # Task should remain available for audit trail

    # ==========================================
    # E2E TEST 2: NetSuite Authentication Failure Recovery
    # ==========================================

    @patch('didero.integrations.erp.clients.netsuite.NetSuiteClient._make_soap_request')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'invalid_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'invalid_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'invalid_token',
        'IONQ_NETSUITE_TOKEN_SECRET': 'invalid_token_secret'
    })
    def test_netsuite_authentication_failure_recovery(self, mock_soap_request):
        """Test graceful handling of NetSuite authentication failures."""
        
        # Mock authentication failure
        auth_error_response = Mock()
        auth_error_response.text = '''
            <platformCore:status isSuccess="false">
                <platformCore:statusDetail>
                    <platformCore:code>INVALID_LOGIN_CREDENTIALS</platformCore:code>
                    <platformCore:message>Invalid login attempt.</platformCore:message>
                </platformCore:statusDetail>
            </platformCore:status>
        '''
        auth_error_response.status_code = 500
        mock_soap_request.return_value = auth_error_response

        # Create and execute task
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        task = create_task_v2(
            task_type="SHIPMENT_CONFIRMATION",
            user=self.ops_user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={"po_number": self.purchase_order.po_number}
        )

        confirm_action = TaskAction.objects.create(
            task=task,
            action_type=self.confirm_action_type,
            execution_params={
                "purchase_order_id": self.purchase_order.id,
                "shipment_id": self.shipment.id
            },
            status="pending"
        )

        handler_context = ActionHandlerContext(task_action_id=confirm_action.id)
        
        result = confirm_shipment(handler_context, {
            "purchase_order_id": self.purchase_order.id,
            "shipment_id": self.shipment.id
        })

        # Should handle error gracefully (non-blocking)
        self.assertIsNone(result.errors)
        self.assertIn("confirmed but ERP sync failed", result.result["message"])
        self.assertIn("Invalid login attempt", result.result["message"])

        # Verify task and shipment data integrity
        self.shipment.refresh_from_db()
        self.purchase_order.refresh_from_db()
        
        self.assertEqual(self.shipment.tracking_number, "1Z999AA10123456784")
        self.assertEqual(self.purchase_order.po_number, "PO12345")

    # ==========================================
    # E2E TEST 3: Multi-Shipment Purchase Order
    # ==========================================

    @patch('didero.integrations.erp.clients.netsuite.NetSuiteClient._make_soap_request')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'valid_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'valid_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'valid_token',
        'IONQ_NETSUITE_TOKEN_SECRET': 'valid_token_secret'
    })
    def test_multi_shipment_purchase_order(self, mock_soap_request):
        """Test handling PO with multiple shipments."""
        
        # Create second shipment for same PO
        shipment2 = Shipment.objects.create(
            purchase_order=self.purchase_order,
            tracking_number="1Z888BB20123456785",
            carrier="FedEx",
            status="shipped",
            shipment_date=date(2024, 1, 20),
            estimated_delivery_date=date(2024, 1, 24)
        )

        # Mock successful NetSuite responses for both shipments
        def mock_soap_response(action, body):
            if action == "search":
                response = Mock()
                response.text = '''
                    <searchResult>
                        <platformCore:record internalId="12345" type="purchaseOrder">
                            <tranPurch:tranId>PO12345</tranPurch:tranId>
                        </platformCore:record>
                    </searchResult>
                '''
                response.status_code = 200
                return response
            else:  # update
                response = Mock()
                response.text = '''
                    <platformCore:status isSuccess="true">
                        <platformCore:message>Purchase order updated successfully</platformCore:message>
                    </platformCore:status>
                '''
                response.status_code = 200
                return response

        mock_soap_request.side_effect = lambda action, body: mock_soap_response(action, body)

        # Create tasks for both shipments
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        # Task for first shipment
        task1 = create_task_v2(
            task_type="SHIPMENT_CONFIRMATION",
            user=self.ops_user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "po_number": self.purchase_order.po_number,
                "tracking_number": self.shipment.tracking_number,
                "shipment_sequence": "1 of 2"
            }
        )

        # Task for second shipment
        task2 = create_task_v2(
            task_type="SHIPMENT_CONFIRMATION",
            user=self.ops_user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={
                "po_number": self.purchase_order.po_number,
                "tracking_number": shipment2.tracking_number,
                "shipment_sequence": "2 of 2"
            }
        )

        # Execute both shipment confirmations
        for task, shipment in [(task1, self.shipment), (task2, shipment2)]:
            confirm_action = TaskAction.objects.create(
                task=task,
                action_type=self.confirm_action_type,
                execution_params={
                    "purchase_order_id": self.purchase_order.id,
                    "shipment_id": shipment.id
                },
                status="pending"
            )

            handler_context = ActionHandlerContext(task_action_id=confirm_action.id)
            
            result = confirm_shipment(handler_context, {
                "purchase_order_id": self.purchase_order.id,
                "shipment_id": shipment.id
            })

            # Verify successful execution
            self.assertIsNone(result.errors)
            self.assertIn("Shipment confirmed and synced to NETSUITE", result.result["message"])

        # Verify both tracking numbers were processed
        self.assertEqual(mock_soap_request.call_count, 4)  # 2 search + 2 update calls

        # Verify different tracking numbers in update calls
        update_calls = [call for call in mock_soap_request.call_args_list if call[1]["action"] == "update"]
        
        first_update_body = update_calls[0][1]["body"]
        second_update_body = update_calls[1][1]["body"]
        
        self.assertIn("1Z999AA10123456784", first_update_body)
        self.assertIn("1Z888BB20123456785", second_update_body)

    # ==========================================
    # E2E TEST 4: Network Timeout and Retry Logic
    # ==========================================

    @patch('didero.integrations.erp.clients.netsuite.NetSuiteClient._make_soap_request')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'valid_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'valid_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'valid_token',
        'IONQ_NETSUITE_TOKEN_SECRET': 'valid_token_secret'
    })
    def test_network_timeout_handling(self, mock_soap_request):
        """Test handling of network timeouts and connection errors."""
        
        # Mock network timeout
        mock_soap_request.side_effect = requests.exceptions.Timeout("Request timed out")

        # Create and execute task
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        task = create_task_v2(
            task_type="SHIPMENT_CONFIRMATION",
            user=self.ops_user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={"po_number": self.purchase_order.po_number}
        )

        confirm_action = TaskAction.objects.create(
            task=task,
            action_type=self.confirm_action_type,
            execution_params={
                "purchase_order_id": self.purchase_order.id,
                "shipment_id": self.shipment.id
            },
            status="pending"
        )

        handler_context = ActionHandlerContext(task_action_id=confirm_action.id)
        
        result = confirm_shipment(handler_context, {
            "purchase_order_id": self.purchase_order.id,
            "shipment_id": self.shipment.id
        })

        # Should handle timeout gracefully
        self.assertIsNone(result.errors)
        self.assertIn("confirmed but ERP sync failed", result.result["message"])
        self.assertIn("Request timed out", result.result["message"])

    # ==========================================
    # E2E TEST 5: Concurrent Task Execution
    # ==========================================

    @patch('didero.integrations.erp.clients.netsuite.NetSuiteClient._make_soap_request')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'valid_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'valid_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'valid_token',
        'IONQ_NETSUITE_TOKEN_SECRET': 'valid_token_secret'
    })
    def test_concurrent_task_execution(self, mock_soap_request):
        """Test system behavior under concurrent task execution."""
        
        # Mock successful responses with slight delay simulation
        def delayed_response(action, body):
            import time
            time.sleep(0.1)  # Simulate network delay
            
            if action == "search":
                response = Mock()
                response.text = '''
                    <searchResult>
                        <platformCore:record internalId="12345" type="purchaseOrder">
                            <tranPurch:tranId>PO12345</tranPurch:tranId>
                        </platformCore:record>
                    </searchResult>
                '''
                response.status_code = 200
                return response
            else:  # update
                response = Mock()
                response.text = '''
                    <platformCore:status isSuccess="true">
                        <platformCore:message>Purchase order updated successfully</platformCore:message>
                    </platformCore:status>
                '''
                response.status_code = 200
                return response

        mock_soap_request.side_effect = lambda action, body: delayed_response(action, body)

        # Create multiple tasks
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        tasks = []
        for i in range(3):
            task = create_task_v2(
                task_type="SHIPMENT_CONFIRMATION",
                user=self.ops_user,
                model_type=po_content_type,
                model_id=str(self.purchase_order.id),
                task_type_params={
                    "po_number": self.purchase_order.po_number,
                    "task_sequence": f"Task {i+1}"
                }
            )
            tasks.append(task)

        # Execute tasks concurrently
        import threading
        
        results = []
        errors = []

        def execute_task(task):
            try:
                confirm_action = TaskAction.objects.create(
                    task=task,
                    action_type=self.confirm_action_type,
                    execution_params={
                        "purchase_order_id": self.purchase_order.id,
                        "shipment_id": self.shipment.id
                    },
                    status="pending"
                )

                handler_context = ActionHandlerContext(task_action_id=confirm_action.id)
                
                result = confirm_shipment(handler_context, {
                    "purchase_order_id": self.purchase_order.id,
                    "shipment_id": self.shipment.id
                })
                
                results.append(result)
            except Exception as e:
                errors.append(str(e))

        # Start concurrent threads
        threads = []
        for task in tasks:
            thread = threading.Thread(target=execute_task, args=(task,))
            threads.append(thread)
            thread.start()

        # Wait for completion
        for thread in threads:
            thread.join()

        # Verify all tasks completed successfully
        self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
        self.assertEqual(len(results), 3)
        
        for result in results:
            self.assertIsNone(result.errors)
            self.assertIn("Shipment confirmed", result.result["message"])

    # ==========================================
    # E2E TEST 6: Complete Error Recovery Flow
    # ==========================================

    @patch('didero.workflows.core.activities.erp_sync.create_erp_sync_error_task')
    @patch('didero.integrations.erp.clients.netsuite.NetSuiteClient._make_soap_request')
    @patch.dict('os.environ', {
        'IONQ_NETSUITE_ACCOUNT_ID': '7581852',
        'IONQ_NETSUITE_CONSUMER_KEY': 'valid_key',
        'IONQ_NETSUITE_CONSUMER_SECRET': 'valid_secret',
        'IONQ_NETSUITE_TOKEN_ID': 'valid_token',
        'IONQ_NETSUITE_TOKEN_SECRET': 'valid_token_secret'
    })
    def test_complete_error_recovery_flow(self, mock_soap_request, mock_create_error_task):
        """Test complete error recovery including error task creation."""
        
        # Mock NetSuite error
        error_response = Mock()
        error_response.text = '''
            <platformCore:status isSuccess="false">
                <platformCore:statusDetail>
                    <platformCore:code>INSUFFICIENT_PERMISSION</platformCore:code>
                    <platformCore:message>You do not have permission to set a value for element custbody_ionq_tracking_number due to one of the following reasons: 1) The field is read-only; 2) An associated feature is disabled; 3) The field is available either when a record is created or updated, but not in both cases.</platformCore:message>
                </platformCore:statusDetail>
            </platformCore:status>
        '''
        error_response.status_code = 500
        mock_soap_request.return_value = error_response

        # Mock error task creation
        mock_create_error_task.return_value = None

        # Create and execute task
        po_content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        task = create_task_v2(
            task_type="SHIPMENT_CONFIRMATION",
            user=self.ops_user,
            model_type=po_content_type,
            model_id=str(self.purchase_order.id),
            task_type_params={"po_number": self.purchase_order.po_number}
        )

        confirm_action = TaskAction.objects.create(
            task=task,
            action_type=self.confirm_action_type,
            execution_params={
                "purchase_order_id": self.purchase_order.id,
                "shipment_id": self.shipment.id
            },
            status="pending"
        )

        handler_context = ActionHandlerContext(task_action_id=confirm_action.id)
        
        result = confirm_shipment(handler_context, {
            "purchase_order_id": self.purchase_order.id,
            "shipment_id": self.shipment.id
        })

        # Verify graceful error handling
        self.assertIsNone(result.errors)
        self.assertIn("confirmed but ERP sync failed", result.result["message"])

        # Verify error task creation was called
        mock_create_error_task.assert_called_once()
        
        call_args = mock_create_error_task.call_args[1]
        self.assertEqual(call_args["shipment"].id, self.shipment.id)
        self.assertIn("INSUFFICIENT_PERMISSION", call_args["error_message"])


if __name__ == "__main__":
    # Run with comprehensive output
    import sys
    
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        suite = pytest.TestSuite()
        suite.addTest(TestManualERPFlowEndToEnd(test_name))
        runner = pytest.TextTestRunner(verbosity=2)
        runner.run(suite)
    else:
        pytest.main([__file__, "-v", "--tb=long", "-s"])