# If you're getting authentication errors run:
# - For dev: aws sso login --profile didero-dev
# - For staging/prod: aws sso login --profile didero-prod
import argparse
import os

import boto3
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up command line argument parsing
parser = argparse.ArgumentParser(description="Create SQS queues for Didero")
parser.add_argument(
    "--env",
    dest="didero_env",
    required=True,
    help="Environment to create queues for (dev, staging, prod)",
)
args = parser.parse_args()

# Validate that the environment exists
didero_env = args.didero_env
valid_environments = ["dev", "staging", "prod"]
if didero_env not in valid_environments:
    raise ValueError(
        f"Environment '{didero_env}' is not valid. Choose from: {', '.join(valid_environments)}"
    )

# Map environment to AWS profile
profile_mapping = {"dev": "didero-dev", "staging": "didero-prod", "prod": "didero-prod"}
profile_name = profile_mapping[didero_env]

session = boto3.Session(profile_name=profile_name)
sqs = session.client("sqs", region_name="us-east-2")


if __name__ == "__main__":
    # For dev environment, use developer name from environment variable
    if didero_env == "dev":
        developer_name = os.environ.get("DIDERO_DEVELOPER_NAME")
        if not developer_name:
            raise ValueError(
                "DIDERO_DEVELOPER_NAME environment variable must be set for dev environment"
            )
        queue_prefix = f"didero-dev-{developer_name}"
        print(f"Creating queues for environment: dev (developer: {developer_name})")
    else:
        queue_prefix = f"didero-{didero_env}"
        print(f"Creating queues for environment: {didero_env}")

    queues = [
        f"{queue_prefix}-ai_workflows",
        f"{queue_prefix}-bulk_supplier_imports",
        f"{queue_prefix}-default",
        f"{queue_prefix}-email_backfills",
        f"{queue_prefix}-periodic_tasks",
        f"{queue_prefix}-task_email_notifications",
        f"{queue_prefix}-nylas_email_notifications",
    ]

    # Create the dead letter queue first
    dlq_name = f"{queue_prefix}-dlq"
    dlq_arn = None

    try:
        dlq_url = sqs.get_queue_url(QueueName=dlq_name)["QueueUrl"]
        dlq_arn = sqs.get_queue_attributes(
            QueueUrl=dlq_url, AttributeNames=["QueueArn"]
        )["Attributes"]["QueueArn"]
        print(f"Dead letter queue '{dlq_name}' already exists with ARN: {dlq_arn}")
    except sqs.exceptions.QueueDoesNotExist:
        print(f"Creating dead letter queue '{dlq_name}'")
        dlq_response = sqs.create_queue(
            QueueName=dlq_name,
            Attributes={
                "MessageRetentionPeriod": "1209600"  # 14 days
            },
        )
        dlq_arn = sqs.get_queue_attributes(
            QueueUrl=dlq_response["QueueUrl"], AttributeNames=["QueueArn"]
        )["Attributes"]["QueueArn"]
        print(f"Created dead letter queue '{dlq_name}' with ARN: {dlq_arn}")

    # Create other queues with the dead letter queue as their RedrivePolicy and custom visibility timeouts
    for queue_name in queues:
        visibility_timeout = "120"  # default 2 minutes
        queue_name_suffix = queue_name.split("-")[-1]
        if queue_name_suffix == "bulk_supplier_imports":
            visibility_timeout = (
                "240"  # 4 minutes as this task generally takes longer than the others.
            )

        # Check if the queue already exists
        try:
            sqs.get_queue_url(QueueName=queue_name)
            print(f"Queue '{queue_name}' already exists")
        except sqs.exceptions.QueueDoesNotExist:
            print(
                f"Creating queue '{queue_name}' with visibility timeout of {visibility_timeout} seconds"
            )

            attributes = {
                "RedrivePolicy": f'{{"deadLetterTargetArn":"{dlq_arn}","maxReceiveCount":"3"}}',
                "VisibilityTimeout": visibility_timeout,
            }

            sqs.create_queue(QueueName=queue_name, Attributes=attributes)
            print(f"Created queue '{queue_name}'")
