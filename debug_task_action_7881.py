#!/usr/bin/env python
"""
Debug script for task action 7881 that's failing to execute.
This script will check every step of the execution process.
"""
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'didero.settings.common')
django.setup()

from didero.tasks.models import TaskAction
from didero.tasks.schemas import ActionHandlerContext
from didero.users.models import User

def debug_task_action(task_action_id: int):
    print(f"🔍 Debugging Task Action {task_action_id}")
    print("=" * 50)
    
    try:
        # Step 1: Get the task action
        task_action = TaskAction.objects.get(id=task_action_id)
        print(f"✅ Task action found: {task_action}")
        print(f"   - Status: {task_action.status}")
        print(f"   - Action Type: {task_action.action_type}")
        print(f"   - Execution Params: {task_action.execution_params}")
        print(f"   - Executed By: {task_action.executed_by}")
        print(f"   - Executed At: {task_action.executed_at}")
        
        # Step 2: Check action type and handler
        if not task_action.action_type:
            print("❌ ERROR: No action type found!")
            return
            
        print(f"\n🔧 Action Type Details:")
        print(f"   - Name: {task_action.action_type.name}")
        print(f"   - Handler Name: {task_action.action_type.handler}")
        
        # Step 3: Try to get the handler
        try:
            handler = task_action.action_type.get_handler()
            if handler:
                print(f"✅ Handler found: {handler}")
            else:
                print("❌ ERROR: Handler is None!")
                return
        except Exception as e:
            print(f"❌ ERROR getting handler: {e}")
            return
            
        # Step 4: Check execution parameters
        exec_params = task_action.execution_params or {}
        print(f"\n📋 Execution Parameters: {exec_params}")
        
        if not exec_params:
            print("❌ ERROR: No execution parameters!")
            return
            
        # Step 5: Check if shipment and PO exist
        if 'shipment_id' in exec_params:
            from didero.orders.models import Shipment
            try:
                shipment = Shipment.objects.get(id=exec_params['shipment_id'])
                print(f"✅ Shipment found: {shipment}")
                print(f"   - Tracking: {shipment.tracking_number}")
                print(f"   - Status: {shipment.status}")
            except Shipment.DoesNotExist:
                print(f"❌ ERROR: Shipment {exec_params['shipment_id']} not found!")
                return
        
        if 'purchase_order_id' in exec_params:
            from didero.orders.models import PurchaseOrder
            try:
                po = PurchaseOrder.objects.get(id=exec_params['purchase_order_id'])
                print(f"✅ Purchase Order found: {po}")
                print(f"   - PO Number: {po.po_number}")
                print(f"   - Team: {po.team}")
            except PurchaseOrder.DoesNotExist:
                print(f"❌ ERROR: PurchaseOrder {exec_params['purchase_order_id']} not found!")
                return
        
        # Step 6: Check team and ERP config
        if 'purchase_order_id' in exec_params:
            from didero.integrations.models import ERPIntegrationConfig
            team = po.team
            print(f"\n🏢 Team: {team}")
            
            try:
                erp_config = ERPIntegrationConfig.objects.get(team=team, erp_type="netsuite")
                print(f"✅ ERP Config found: {erp_config.enabled}")
                print(f"   - Config: {erp_config.config}")
            except ERPIntegrationConfig.DoesNotExist:
                print("❌ ERROR: No NetSuite ERP config for this team!")
                
        # Step 7: Check environment variables for IonQ
        ionq_vars = [
            'IONQ_NETSUITE_ACCOUNT_ID',
            'IONQ_NETSUITE_CONSUMER_KEY', 
            'IONQ_NETSUITE_CONSUMER_SECRET',
            'IONQ_NETSUITE_TOKEN_ID',
            'IONQ_NETSUITE_TOKEN_SECRET'
        ]
        
        print(f"\n🔐 Environment Variables Check:")
        missing_vars = []
        for var in ionq_vars:
            if os.environ.get(var):
                print(f"   ✅ {var}: {'*' * 10}")  # Mask the value
            else:
                print(f"   ❌ {var}: Missing!")
                missing_vars.append(var)
                
        if missing_vars:
            print(f"❌ ERROR: Missing environment variables: {missing_vars}")
            
        # Step 8: Try to manually execute the handler
        print(f"\n🚀 Attempting Manual Handler Execution:")
        print(f"Handler: {handler}")
        print(f"Context: task_action_id={task_action.id}")
        print(f"Params: {exec_params}")
        
        try:
            context = ActionHandlerContext(task_action_id=task_action.id)
            result = handler(context, exec_params)
            print(f"✅ Handler executed successfully!")
            print(f"   - Result: {result}")
            print(f"   - Errors: {result.errors if hasattr(result, 'errors') else 'No errors attr'}")
            
            if hasattr(result, 'errors') and result.errors:
                print(f"❌ Handler returned errors: {result.errors}")
            else:
                print(f"✅ Handler completed successfully!")
                
        except Exception as e:
            print(f"❌ Handler execution failed: {e}")
            import traceback
            traceback.print_exc()
            
        print("\n" + "=" * 50)
        print("Debug complete!")
        
    except TaskAction.DoesNotExist:
        print(f"❌ ERROR: Task action {task_action_id} not found!")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_task_action(7881)