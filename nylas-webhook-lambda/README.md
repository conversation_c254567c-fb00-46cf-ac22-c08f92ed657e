# Nylas Webhook Lambda

This Lambda function receives webhooks from <PERSON><PERSON><PERSON> and forwards them to developer-specific SQS queues for processing by Celery workers.

## Features

- **Developer-specific resources**: Each developer gets their own Lambda function and SQS queue
- **Automatic setup**: The deploy script creates all necessary AWS resources
- **SQS Extended Client**: Supports large messages (>256KB) by storing them in S3
- **Celery-compatible**: Messages are formatted for direct consumption by Celery workers

## Prerequisites

1. AWS CLI configured with `didero-dev` profile
2. Python 3.11+ installed  
3. `jq` command-line tool installed (for JSON parsing)
4. `DIDERO_DEVELOPER_NAME` set in the root `.env` file or environment
5. `DideroNylasWebhookDeveloperPolicy` attached to your IAM user (ask admin)

## Deployment

### Quick Start

```bash
# Deploy without setting webhook secret
./deploy.sh

# Deploy and set webhook secret
./deploy.sh your-webhook-secret-here

# Show usage information
./deploy.sh --help
```

The script will:
1. Load environment variables from the root `.env` file
2. Use your developer name from `DIDERO_DEVELOPER_NAME`
3. Check for the shared IAM role (`didero-shared-nylas-lambda-role`)
4. Create a Lambda function named `didero-dev-{developer}-nylas-webhook`
5. Create an SQS queue named `didero-dev-{developer}-nylas_email_notifications`
6. Deploy the Lambda code
7. Optionally set/update the Nylas webhook secret
8. Output the Lambda function URL for webhook configuration


### Using a Different AWS Profile

```bash
AWS_PROFILE=my-profile ./deploy.sh
```

## Configuration

### Environment Variables

The Lambda function uses these environment variables (automatically set by deploy script):

- `DEVELOPER_NAME`: Your developer identifier
- `ENVIRONMENT`: Always "dev" for development
- `SQS_EXTENDED_S3_BUCKET`: S3 bucket for large messages (shared: `didero-dev-sqs-extended`)
- `NYLAS_WEBHOOK_SECRET`: (Optional) Secret for webhook signature verification

### Setting the Webhook Secret

The webhook secret can be set during deployment:

```bash
# Set webhook secret during deployment
./deploy.sh your-secret-here

# Or update an existing deployment
./deploy.sh your-new-secret-here
```

The deploy script will preserve all other environment variables when updating the webhook secret.

## Nylas Webhook Configuration

After deployment, configure your Nylas webhook:

1. Log into your Nylas dashboard
2. Navigate to Webhooks
3. Create a new webhook with:
   - **URL**: The function URL output by the deploy script
   - **Events**: Select the events you want to receive (e.g., `message.created`)
   - **Secret**: (Optional) Set a webhook secret for signature verification

## Local Development

### Running Celery Worker

To process messages from your queue:

```bash
# In the didero-api directory
AWS_PROFILE=didero-dev make celery-nylas
```

Or manually:

```bash
AWS_PROFILE=didero-dev DJANGO_SETTINGS_MODULE=didero.settings.common \
  uv run celery -A didero worker -Q nylas_email_notifications --loglevel=info
```

### Testing the Lambda

Send a test webhook:

```bash
aws lambda invoke \
  --function-name didero-dev-{developer}-nylas-webhook \
  --invocation-type RequestResponse \
  --payload '{"httpMethod":"POST","body":"{\"type\":\"test\",\"data\":{}}","headers":{}}' \
  --profile didero-dev \
  response.json
```

### Viewing Logs

```bash
aws logs tail /aws/lambda/didero-dev-{developer}-nylas-webhook \
  --profile didero-dev \
  --follow
```

## Message Format

The Lambda converts Nylas webhooks to Celery-compatible messages:

```json
{
  "body": "base64-encoded([[webhook_json], {}, embed])",
  "headers": {
    "task": "didero.emails.tasks.nylas_notifications.process_nylas_notification",
    "id": "task-uuid"
  },
  "properties": {
    "body_encoding": "base64",
    "delivery_info": {
      "exchange": "",
      "routing_key": "nylas_email_notifications"
    }
  },
  "content-type": "application/json",
  "content-encoding": "utf-8",
  "exchange": "",
  "routing_key": "nylas_email_notifications"
}
```

## Troubleshooting

### Lambda Deployment Fails

- Ensure AWS CLI is configured: `aws configure list --profile didero-dev`
- Check IAM permissions for creating Lambda functions and roles

### Messages Not Processing

1. Check Lambda logs for errors
2. Verify SQS queue has messages: 
   ```bash
   aws sqs get-queue-attributes \
     --queue-url https://sqs.us-east-2.amazonaws.com/{account}/didero-dev-{developer}-nylas_email_notifications \
     --attribute-names ApproximateNumberOfMessages \
     --profile didero-dev
   ```
3. Ensure Celery worker is running and connected to the correct queue

### Webhook Signature Verification Fails

- Verify the `NYLAS_WEBHOOK_SECRET` environment variable matches your Nylas webhook configuration
- Check that the webhook payload hasn't been modified by a proxy

## Cleanup

To remove all resources created for a developer:

```bash
# Delete Lambda function
aws lambda delete-function \
  --function-name didero-dev-{developer}-nylas-webhook \
  --profile didero-dev

# Delete IAM role (after detaching policies)
aws iam delete-role-policy \
  --role-name didero-dev-{developer}-nylas-lambda-role \
  --policy-name SQSAccess \
  --profile didero-dev

aws iam delete-role-policy \
  --role-name didero-dev-{developer}-nylas-lambda-role \
  --policy-name S3ExtendedClientAccess \
  --profile didero-dev

aws iam detach-role-policy \
  --role-name didero-dev-{developer}-nylas-lambda-role \
  --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole \
  --profile didero-dev

aws iam delete-role \
  --role-name didero-dev-{developer}-nylas-lambda-role \
  --profile didero-dev

# Delete SQS queue
aws sqs delete-queue \
  --queue-url https://sqs.us-east-2.amazonaws.com/{account}/didero-dev-{developer}-nylas_email_notifications \
  --profile didero-dev
```

## Architecture

```
Nylas Webhook → Lambda Function → SQS Queue → Celery Worker → Django
                        ↓
                 S3 (for large messages)
```

Each developer has their own isolated pipeline, preventing conflicts during development.
