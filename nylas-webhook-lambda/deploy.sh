#!/bin/bash
set -e  # Exit on any error

# Usage information
show_usage() {
    echo "Usage: ./deploy.sh [--env ENVIRONMENT] [--create-webhook] [webhook_secret]"
    echo ""
    echo "Deploy or update the <PERSON>ylas webhook Lambda function and related resources."
    echo ""
    echo "Options:"
    echo "  --env ENVIRONMENT    Set environment: dev, staging, or prod (default: dev)"
    echo "  --create-webhook     Automatically create webhook in Nylas (requires NYLAS_API_KEY)"
    echo "  webhook_secret       Optional: Set or update the Nylas webhook secret"
    echo ""
    echo "Examples:"
    echo "  ./deploy.sh                              # Deploy to dev environment"
    echo "  ./deploy.sh --env staging                # Deploy to staging"
    echo "  ./deploy.sh --create-webhook             # Deploy and create webhook automatically"
    echo "  ./deploy.sh --env prod --create-webhook  # Deploy to prod and create webhook"
    echo "  ./deploy.sh --env staging abc123         # Deploy to staging with webhook secret"
    echo ""
    echo "Requirements:"
    echo "  - For dev: DIDERO_DEVELOPER_NAME must be set in .env file"
    echo "  - AWS CLI configured with didero-dev (dev) or didero-prod (staging/prod) profile"
    echo "  - Python 3.11+ and jq installed"
    echo "  - For --create-webhook: NYLAS_API_KEY must be set in .env file"
}

# Check for help flag
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

# Parse arguments
ENVIRONMENT="dev"
WEBHOOK_SECRET=""
CREATE_WEBHOOK=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --create-webhook)
            CREATE_WEBHOOK=true
            shift
            ;;
        -*)
            echo "Unknown option $1"
            show_usage
            exit 1
            ;;
        *)
            WEBHOOK_SECRET="$1"
            shift
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    echo "Error: Environment must be 'dev', 'staging', or 'prod'"
    exit 1
fi

# Handle developer name for dev environment only
if [ "$ENVIRONMENT" = "dev" ]; then
    # Check if DIDERO_DEVELOPER_NAME is already loaded in environment
    if [ -z "$DIDERO_DEVELOPER_NAME" ]; then
        # Load environment variables from root .env file if it exists
        # Only load the DIDERO_DEVELOPER_NAME variable to avoid complex parsing issues
        if [ -f ../../.env ]; then
            echo "Loading DIDERO_DEVELOPER_NAME from ../../.env file..."
            # Extract just the DIDERO_DEVELOPER_NAME variable
            DIDERO_DEVELOPER_NAME=$(grep "^DIDERO_DEVELOPER_NAME=" ../../.env | cut -d'=' -f2 | cut -d'#' -f1 | tr -d ' "'\''')
            export DIDERO_DEVELOPER_NAME
        elif [ -f ../.env ]; then
            echo "Loading DIDERO_DEVELOPER_NAME from ../.env file..."
            # Extract just the DIDERO_DEVELOPER_NAME variable
            DIDERO_DEVELOPER_NAME=$(grep "^DIDERO_DEVELOPER_NAME=" ../.env | cut -d'=' -f2 | cut -d'#' -f1 | tr -d ' "'\''')
            export DIDERO_DEVELOPER_NAME
        fi
    else
        echo "✓ Using pre-loaded DIDERO_DEVELOPER_NAME from environment"
    fi

    # Get developer name from DIDERO_DEVELOPER_NAME environment variable
    DEVELOPER_NAME=${DIDERO_DEVELOPER_NAME}
    if [ -z "$DEVELOPER_NAME" ]; then
        echo "Error: DIDERO_DEVELOPER_NAME environment variable is not set."
        echo "Please set it in the root .env file or environment (e.g., DIDERO_DEVELOPER_NAME=brien)"
        exit 1
    fi
else
    # For staging/prod, no developer name needed
    DEVELOPER_NAME=""
fi

# Handle Nylas API key for webhook creation
if [ "$CREATE_WEBHOOK" = true ]; then
    # Check if NYLAS_API_KEY is already loaded in environment (for staging/prod ECS)
    if [ -n "$NYLAS_API_KEY" ]; then
        echo "✓ Using pre-loaded NYLAS_API_KEY from environment"
    else
        # Load NYLAS_API_KEY from .env file if it exists (for dev)
        if [ -f ../../.env ]; then
            echo "Loading NYLAS_API_KEY from ../../.env file..."
            # Extract just the NYLAS_API_KEY variable
            NYLAS_API_KEY=$(grep "^NYLAS_API_KEY=" ../../.env | cut -d'=' -f2 | cut -d'#' -f1 | tr -d ' "'"'"'')
            export NYLAS_API_KEY
        elif [ -f ../.env ]; then
            echo "Loading NYLAS_API_KEY from ../.env file..."
            # Extract just the NYLAS_API_KEY variable
            NYLAS_API_KEY=$(grep "^NYLAS_API_KEY=" ../.env | cut -d'=' -f2 | cut -d'#' -f1 | tr -d ' "'"'"'')
            export NYLAS_API_KEY
        fi
    fi
    
    if [ -z "$NYLAS_API_KEY" ]; then
        echo "Error: NYLAS_API_KEY environment variable is not set."
        echo "For dev: Set it in the root .env file"
        echo "For staging/prod: Ensure it's loaded in the ECS environment"
        echo "You can still deploy without --create-webhook and create the webhook manually"
        exit 1
    fi
    
    echo "✓ NYLAS_API_KEY ready for webhook creation"
fi

# AWS Configuration based on environment
if [ "$ENVIRONMENT" = "dev" ]; then
    AWS_PROFILE=${AWS_PROFILE:-didero-dev}
    SHARED_ROLE_NAME="didero-shared-nylas-lambda-role"
    S3_BUCKET_NAME="didero-dev-sqs-extended"
    LAMBDA_FUNCTION_NAME="didero-dev-${DEVELOPER_NAME}-nylas-webhook"
    SQS_QUEUE_NAME="didero-dev-${DEVELOPER_NAME}-nylas_email_notifications"
else
    AWS_PROFILE=${AWS_PROFILE:-didero-prod}
    SHARED_ROLE_NAME="didero-shared-nylas-lambda-role-${ENVIRONMENT}"
    S3_BUCKET_NAME="didero-${ENVIRONMENT}-sqs-extended"
    LAMBDA_FUNCTION_NAME="didero-${ENVIRONMENT}-nylas-webhook"
    SQS_QUEUE_NAME="didero-${ENVIRONMENT}-nylas_email_notifications"
fi

AWS_REGION=${AWS_REGION:-us-east-2}
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --profile $AWS_PROFILE --query Account --output text)
LAMBDA_ROLE_ARN="arn:aws:iam::${AWS_ACCOUNT_ID}:role/${SHARED_ROLE_NAME}"
SQS_QUEUE_URL="https://sqs.${AWS_REGION}.amazonaws.com/${AWS_ACCOUNT_ID}/${SQS_QUEUE_NAME}"

# Function to create webhook in Nylas
create_nylas_webhook() {
    local function_url="$1"
    local description="Didero ${ENVIRONMENT}"
    if [ ! -z "$DEVELOPER_NAME" ]; then
        description="Didero ${ENVIRONMENT} - ${DEVELOPER_NAME}"
    fi
    
    echo "Creating webhook in Nylas..." >&2
    
    # Create webhook payload with the trigger types we actually use
    local webhook_payload=$(cat <<EOF
{
    "trigger_types": [
        "message.created",
        "grant.deleted"
    ],
    "description": "$description",
    "webhook_url": "$function_url"
}
EOF
)
    
    # Call Nylas API to create webhook
    local response=$(curl -s -X POST \
        "https://api.us.nylas.com/v3/webhooks/" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $NYLAS_API_KEY" \
        -d "$webhook_payload")
    
    # Check if request was successful
    if echo "$response" | jq -e '.request_id' > /dev/null 2>&1; then
        # Extract webhook details
        local webhook_id=$(echo "$response" | jq -r '.data.id')
        local webhook_secret=$(echo "$response" | jq -r '.data.webhook_secret')
        
        echo "✓ Webhook created successfully!" >&2
        echo "  Webhook ID: $webhook_id" >&2
        echo "  Description: $description" >&2
        echo "  URL: $function_url" >&2
        echo "  Triggers: message.created, grant.deleted" >&2
        
        # Return ONLY the webhook secret (to stdout)
        echo "$webhook_secret"
        return 0
    else
        echo "❌ Failed to create webhook in Nylas" >&2
        echo "Response: $response" >&2
        echo "You can create the webhook manually in the Nylas dashboard" >&2
        return 1
    fi
}

echo "========================================="
echo "Environment: $ENVIRONMENT"
echo "AWS Profile: $AWS_PROFILE"
if [ ! -z "$DEVELOPER_NAME" ]; then
    echo "Developer: $DEVELOPER_NAME"
fi
echo "Lambda Function: $LAMBDA_FUNCTION_NAME"
echo "SQS Queue: $SQS_QUEUE_NAME"
if [ ! -z "$WEBHOOK_SECRET" ]; then
    echo "Webhook Secret: Will be updated"
fi
echo "========================================="

# Check if Lambda function exists
echo "Checking if Lambda function exists..."
if aws lambda get-function --function-name $LAMBDA_FUNCTION_NAME --profile $AWS_PROFILE &>/dev/null; then
    FUNCTION_EXISTS=true
    echo "Lambda function exists. Will update code."
else
    FUNCTION_EXISTS=false
    echo "Lambda function does not exist. Will create new function."
fi

# Trust that shared IAM role exists (will fail gracefully if it doesn't)
echo "Using shared IAM role: $SHARED_ROLE_NAME"
echo "Note: If Lambda creation fails, ask admin to verify the role exists"

# Check if SQS queue exists
if ! aws sqs get-queue-url --queue-name $SQS_QUEUE_NAME --profile $AWS_PROFILE &>/dev/null; then
    echo "Creating SQS queue: $SQS_QUEUE_NAME"
    aws sqs create-queue \
        --queue-name $SQS_QUEUE_NAME \
        --profile $AWS_PROFILE \
        --attributes '{"MessageRetentionPeriod":"1209600","VisibilityTimeout":"60"}'
else
    echo "SQS queue already exists."
fi

# Clean up any previous deployment package
rm -f deployment-package.zip

# Create a temporary directory for the deployment package
temp_dir=$(mktemp -d)

# Copy the Lambda function
cp nylas-webhook-lambda.py $temp_dir/

# Install dependencies directly into temp directory
echo "Installing dependencies..."
pip3 install -r requirements.txt -t $temp_dir/ --quiet || { echo "Failed to install dependencies"; rm -rf $temp_dir; exit 1; }

# Create the deployment package
cd $temp_dir
zip -rq deployment-package.zip .
mv deployment-package.zip "$OLDPWD/"
cd "$OLDPWD"

# Clean up
rm -rf $temp_dir

echo "Deployment package created: deployment-package.zip"
echo "Size: $(du -h deployment-package.zip | cut -f1)"

# Prepare environment variables
if [ "$FUNCTION_EXISTS" = true ]; then
    # Get existing environment variables
    echo "Fetching current environment variables..."
    CURRENT_ENV=$(aws lambda get-function-configuration \
        --function-name $LAMBDA_FUNCTION_NAME \
        --profile $AWS_PROFILE \
        --query 'Environment.Variables' \
        --output json 2>/dev/null || echo "null")
else
    CURRENT_ENV="null"
fi

# Build environment variables
if [ "$CURRENT_ENV" = "null" ] || [ -z "$CURRENT_ENV" ]; then
    # Create new environment variables
    ENV_VARS="{\"ENVIRONMENT\":\"$ENVIRONMENT\",\"SQS_EXTENDED_S3_BUCKET\":\"$S3_BUCKET_NAME\""
    if [ ! -z "$DEVELOPER_NAME" ]; then
        ENV_VARS="${ENV_VARS},\"DEVELOPER_NAME\":\"$DEVELOPER_NAME\""
    fi
    if [ ! -z "$WEBHOOK_SECRET" ]; then
        ENV_VARS="${ENV_VARS},\"NYLAS_WEBHOOK_SECRET\":\"$WEBHOOK_SECRET\""
    fi
    ENV_VARS="${ENV_VARS}}"
else
    # Update existing environment variables
    ENV_VARS=$CURRENT_ENV
    if [ ! -z "$WEBHOOK_SECRET" ]; then
        ENV_VARS=$(echo $ENV_VARS | jq -c --arg secret "$WEBHOOK_SECRET" '. + {NYLAS_WEBHOOK_SECRET: $secret}')
    fi
    # Always update environment
    ENV_VARS=$(echo $ENV_VARS | jq -c --arg env "$ENVIRONMENT" '. + {ENVIRONMENT: $env}')
fi

if [ "$FUNCTION_EXISTS" = true ]; then
    # Update existing Lambda function code
    echo "Updating Lambda function code..."
    aws lambda update-function-code \
        --function-name $LAMBDA_FUNCTION_NAME \
        --zip-file fileb://deployment-package.zip \
        --profile $AWS_PROFILE > /dev/null || { echo "Failed to update Lambda function"; exit 1; }
    
    # Wait for the code update to complete before updating configuration
    echo "Waiting for code update to complete..."
    aws lambda wait function-updated \
        --function-name $LAMBDA_FUNCTION_NAME \
        --profile $AWS_PROFILE
    
    # Update environment variables
    echo "Updating Lambda environment variables..."
    echo "{\"Variables\":$ENV_VARS}" > env-vars.json
    aws lambda update-function-configuration \
        --function-name $LAMBDA_FUNCTION_NAME \
        --environment file://env-vars.json \
        --profile $AWS_PROFILE > /dev/null
    rm -f env-vars.json
else
    # Create new Lambda function
    echo "Creating Lambda function..."
    echo "{\"Variables\":$ENV_VARS}" > env-vars.json
    aws lambda create-function \
        --function-name $LAMBDA_FUNCTION_NAME \
        --runtime python3.11 \
        --role $LAMBDA_ROLE_ARN \
        --handler nylas-webhook-lambda.lambda_handler \
        --zip-file fileb://deployment-package.zip \
        --timeout 30 \
        --memory-size 128 \
        --environment file://env-vars.json \
        --profile $AWS_PROFILE > /dev/null
    rm -f env-vars.json
    
    # Create function URL for the Lambda
    echo "Creating Lambda function URL..."
    aws lambda create-function-url-config \
        --function-name $LAMBDA_FUNCTION_NAME \
        --auth-type NONE \
        --profile $AWS_PROFILE > lambda-url-output.json
    
    # Add permission for public function URL access
    echo "Adding function URL permissions..."
    aws lambda add-permission \
        --function-name $LAMBDA_FUNCTION_NAME \
        --statement-id FunctionURLAllowPublicAccess \
        --action lambda:InvokeFunctionUrl \
        --principal "*" \
        --function-url-auth-type NONE \
        --profile $AWS_PROFILE > /dev/null 2>&1 || echo "Permission may already exist"
    
    FUNCTION_URL=$(jq -r '.FunctionUrl' lambda-url-output.json)
    rm lambda-url-output.json
    
    echo ""
    echo "========================================="
    echo "Lambda function created successfully!"
    echo ""
    echo "Function URL: $FUNCTION_URL"
    echo ""
    echo "IMPORTANT: Configure this URL in your Nylas webhook settings"
    echo "========================================="
fi

# Get the function URL if it exists
if [ "$FUNCTION_EXISTS" = true ]; then
    FUNCTION_URL=$(aws lambda get-function-url-config \
        --function-name $LAMBDA_FUNCTION_NAME \
        --profile $AWS_PROFILE 2>/dev/null | jq -r '.FunctionUrl')
    
    if [ ! -z "$FUNCTION_URL" ] && [ "$FUNCTION_URL" != "null" ]; then
        echo ""
        echo "========================================="
        echo "Lambda function updated successfully!"
        echo ""
        echo "Function URL: $FUNCTION_URL"
        echo ""
        echo "SQS Queue: $SQS_QUEUE_NAME"
        if [ ! -z "$WEBHOOK_SECRET" ]; then
            echo "Webhook Secret: Updated"
        fi
        echo "========================================="
    else
        echo ""
        echo "Lambda function updated successfully!"
        echo "Note: No function URL configured. Create one if needed."
    fi
fi

# Create webhook in Nylas if requested
if [ "$CREATE_WEBHOOK" = true ] && [ ! -z "$FUNCTION_URL" ] && [ "$FUNCTION_URL" != "null" ]; then
    echo ""
    echo "========================================="
    echo "Creating Nylas Webhook..."
    echo "========================================="
    
    # Call the webhook creation function and capture the webhook secret
    CREATED_WEBHOOK_SECRET=$(create_nylas_webhook "$FUNCTION_URL")
    
    if [ $? -eq 0 ] && [ ! -z "$CREATED_WEBHOOK_SECRET" ]; then
        echo ""
        echo "Updating Lambda with webhook secret..."
        
        # Get current environment variables
        CURRENT_ENV=$(aws lambda get-function-configuration \
            --function-name $LAMBDA_FUNCTION_NAME \
            --profile $AWS_PROFILE \
            --query 'Environment.Variables' \
            --output json 2>/dev/null || echo "null")
        
        # Add webhook secret to environment variables
        if [ "$CURRENT_ENV" = "null" ] || [ -z "$CURRENT_ENV" ]; then
            UPDATED_ENV_VARS="{\"NYLAS_WEBHOOK_SECRET\":\"$CREATED_WEBHOOK_SECRET\"}"
        else
            UPDATED_ENV_VARS=$(echo $CURRENT_ENV | jq -c --arg secret "$CREATED_WEBHOOK_SECRET" '. + {NYLAS_WEBHOOK_SECRET: $secret}')
        fi
        
        # Update Lambda environment variables
        echo "{\"Variables\":$UPDATED_ENV_VARS}" > webhook-env-vars.json
        aws lambda update-function-configuration \
            --function-name $LAMBDA_FUNCTION_NAME \
            --environment file://webhook-env-vars.json \
            --profile $AWS_PROFILE > /dev/null
        rm -f webhook-env-vars.json
        
        echo "✓ Lambda updated with webhook secret"
        echo ""
        echo "========================================="
        echo "🎉 Webhook Setup Complete!"
        echo "========================================="
        echo "Your Lambda is fully configured and connected to Nylas!"
        echo "Webhook will receive: message.created, grant.deleted events"
    else
        echo ""
        echo "⚠️  Webhook creation failed, but Lambda deployment succeeded"
        echo "You can create the webhook manually in Nylas dashboard"
    fi
elif [ "$CREATE_WEBHOOK" = true ]; then
    echo ""
    echo "⚠️  Cannot create webhook: Function URL not available"
    echo "You can create the webhook manually in Nylas dashboard"
fi

# Clean up deployment package
rm -f deployment-package.zip

echo ""
echo "Deployment complete!"