import base64
import hashlib
import hmac
import json
import logging
import os
import uuid

import boto3

logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Import the extended client functionality
import sqs_extended_client

# Initialize SQS Extended Client with S3 support
s3_bucket = os.environ.get("SQS_EXTENDED_S3_BUCKET", "didero-dev-sqs-extended")
sqs = boto3.client("sqs", region_name="us-east-2")

# Configure extended client properties
sqs.large_payload_support = s3_bucket
sqs.use_legacy_attribute = False  # Use new attribute format
sqs.message_size_threshold = 262144  # 256KB (only use S3 for larger messages)


def verify_nylas_signature(
    request_body: bytes, signature: str, webhook_secret: str
) -> bool:
    """
    Verify Nylas webhook signature using HMAC-SHA256.

    Args:
        request_body: The raw request body bytes
        signature: The signature from x-nylas-signature header
        webhook_secret: The webhook secret from environment

    Returns:
        True if signature is valid, False otherwise
    """
    try:
        expected_signature = hmac.new(
            webhook_secret.encode("utf-8"), request_body, hashlib.sha256
        ).hexdigest()

        # Use compare_digest to prevent timing attacks
        return hmac.compare_digest(expected_signature, signature)
    except Exception as e:
        logger.error(f"Error verifying signature: {str(e)}")
        return False


def get_queue_url():
    """Get the appropriate SQS queue URL based on environment."""
    env = os.environ.get("ENVIRONMENT", "dev")

    if env == "dev":
        # For dev, use developer-specific queue
        developer = os.environ.get("DEVELOPER_NAME", "default")
        return f"https://sqs.us-east-2.amazonaws.com/703671904837/didero-dev-{developer}-nylas_email_notifications"
    else:
        # For staging/prod, use environment variable
        queue_url = os.environ.get("SQS_QUEUE_URL")
        if not queue_url:
            raise ValueError(f"SQS_QUEUE_URL not set for environment: {env}")
        return queue_url


def lambda_handler(event, context):
    """
    Handle Nylas webhook requests:
    - GET requests with challenge parameter for verification
    - POST requests with webhook data to forward to SNS
    """
    logger.info(f"Received event: {json.dumps(event)}")

    # Handle GET request with challenge parameter
    http_method = event.get("httpMethod") or event.get("requestContext", {}).get(
        "http", {}
    ).get("method")
    if http_method == "GET":
        query_params = event.get("queryStringParameters", {})
        if query_params and "challenge" in query_params:
            challenge = query_params["challenge"]
            logger.info(f"Responding to challenge: {challenge}")
            return {
                "statusCode": 200,
                "body": challenge,
                "headers": {"Content-Type": "text/plain"},
            }
        else:
            return {
                "statusCode": 400,
                "body": "Missing challenge parameter",
                "headers": {"Content-Type": "text/plain"},
            }

    # Handle POST request (webhook)
    elif http_method == "POST":
        try:
            # Get signature from headers (case-insensitive)
            headers = event.get("headers", {})
            # Convert headers to lowercase for case-insensitive lookup
            headers_lower = {k.lower(): v for k, v in headers.items()}
            signature = headers_lower.get("x-nylas-signature")

            # Get webhook secret from environment
            webhook_secret = os.environ.get("NYLAS_WEBHOOK_SECRET")

            # Only verify signature if webhook secret is configured
            if webhook_secret:
                if not signature:
                    logger.warning("Missing x-nylas-signature header")
                    return {
                        "statusCode": 401,
                        "body": json.dumps({"error": "Missing signature"}),
                        "headers": {"Content-Type": "application/json"},
                    }

                # Get the raw body for signature verification
                raw_body = event.get("body", "")
                if event.get("isBase64Encoded", False):
                    body_bytes = base64.b64decode(raw_body)
                    body = body_bytes.decode("utf-8")  # Decode once and reuse
                else:
                    body_bytes = raw_body.encode("utf-8")
                    body = raw_body  # Use as-is

                # Verify signature
                if not verify_nylas_signature(body_bytes, signature, webhook_secret):
                    logger.warning("Invalid webhook signature")
                    return {
                        "statusCode": 401,
                        "body": json.dumps({"error": "Invalid signature"}),
                        "headers": {"Content-Type": "application/json"},
                    }

                logger.info("Webhook signature verified successfully")
            else:
                logger.warning(
                    "NYLAS_WEBHOOK_SECRET not configured - skipping signature verification"
                )
                # Get the body string for processing (no signature verification case)
                body = event.get("body", "")
                if event.get("isBase64Encoded", False):
                    body = base64.b64decode(body).decode("utf-8")

            # Log webhook type for debugging
            try:
                webhook_data = json.loads(body)
                webhook_type = webhook_data.get("type", "unknown")
                logger.info(f"Processing webhook type: {webhook_type}")
            except:
                logger.info("Could not parse webhook body for logging")

            # Get queue URL
            queue_url = get_queue_url()

            # Create the exact Celery message format that SQS Extended Client expects
            task_id = str(uuid.uuid4())

            # Inner 3-tuple format: [args, kwargs, metadata]
            inner_body = [
                [body],  # args - webhook JSON as first argument
                {},  # kwargs - empty dictionary
                {  # metadata with callbacks, errbacks, chain, chord
                    "callbacks": None,
                    "errbacks": None,
                    "chain": None,
                    "chord": None,
                },
            ]

            # Outer message format that SQS Extended Client expects
            celery_message = {
                "body": base64.b64encode(json.dumps(inner_body).encode()).decode(),
                "content-encoding": "utf-8",
                "content-type": "application/json",
                "headers": {
                    "lang": "py",
                    "task": "didero.emails.tasks.nylas_notifications.process_nylas_notification",
                    "id": task_id,
                    "shadow": None,
                    "eta": None,
                    "expires": None,
                    "group": None,
                    "group_index": None,
                    "retries": 0,
                    "timelimit": [None, None],
                    "root_id": task_id,
                    "parent_id": None,
                    "argsrepr": f"['{body[:50]}...']",
                    "kwargsrepr": "{}",
                    "origin": "lambda-webhook",
                    "ignore_result": False,
                    "replaced_task_nesting": 0,
                    "stamped_headers": None,
                    "stamps": {},
                },
                "properties": {
                    "correlation_id": task_id,
                    "reply_to": str(uuid.uuid4()),
                    "delivery_mode": 2,
                    "delivery_info": {
                        "exchange": "",
                        "routing_key": "nylas_email_notifications",
                    },
                    "priority": 0,
                    "body_encoding": "base64",
                    "delivery_tag": str(uuid.uuid4()),
                },
            }

            response = sqs.send_message(
                QueueUrl=queue_url, MessageBody=json.dumps(celery_message)
            )

            logger.info(f"Successfully sent to SQS: {response['MessageId']}")

            return {
                "statusCode": 200,
                "body": json.dumps(
                    {"status": "success", "messageId": response["MessageId"]}
                ),
                "headers": {"Content-Type": "application/json"},
            }
        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}", exc_info=True)
            return {
                "statusCode": 500,
                "body": json.dumps(
                    {"error": "Internal server error", "message": str(e)}
                ),
                "headers": {"Content-Type": "application/json"},
            }

    # Method not allowed
    return {
        "statusCode": 405,
        "body": json.dumps({"error": "Method not allowed"}),
        "headers": {"Content-Type": "application/json"},
    }
