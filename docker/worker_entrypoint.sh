#!/bin/bash
set -e

# PLEASE AVOID USING THE DEFAULT QUEUE
# It is here because some external libraries don't let you specifiy a queue
# We want to try and categorize tasks the best we can
# If you need to add a new queue, please add it to the celery_supervisord.conf file as well. As in production/staging environments we run using supervisord.
# This script is only used for local development.
uv run celery -A didero worker --loglevel=info -Q default,email_backfills,ai_workflows,bulk_supplier_imports,periodic_tasks,task_email_notifications,nylas_email_notifications &
uv run celery -A didero beat -l info &

wait

# We will use the below once we have supported multiple workers (one queue each);
# for now, we will keep one worker that runs both celery beat and all the queues.


# if [ "$WORKER_TYPE" == "beat" ]; then
#     echo "Starting Celery Beat..."
#     uv run celery -A didero beat --loglevel=info
# elif [ "$WORKER_TYPE" == "default" ]; then
#     echo "Starting Celery Worker for default queue..."
#     uv run celery -A didero worker --loglevel=info -Q default
# elif [ "$WORKER_TYPE" == "email_backfills" ]; then
#     echo "Starting Celery Worker for email_import queue..."
#     uv run celery -A didero worker --loglevel=info -Q email_import
# else
#     echo "Unknown WORKER_TYPE: $WORKER_TYPE"
#     exit 1
# fi
