# Testing Nylas Webhooks with Dev Account

## Prerequisites

1. Dev Nylas account with API access
2. AWS CLI configured with `didero-dev` profile
3. `DIDERO_DEVELOPER_NAME` set in your `.env` file
4. SQS queue created: `didero-dev-{developer_name}-nylas_email_notifications`

## Step 1: Create/Update Webhook in Nylas

### Option A: Using Nylas Dashboard (Easier)

1. Log into your Nylas Dashboard
2. Navigate to Notifications → Webhooks
3. Create a new webhook with:
   - **URL**: `https://sns.us-east-2.amazonaws.com/?Action=Publish&TopicArn=arn:aws:sns:us-east-2:************:didero-dev-nylas-webhooks`
   - **Events**: Select `message.created` and `grant.expired`
   - **Description**: "Dev testing webhook"

### Option B: Using Python Script

Create `test_webhook_setup.py`:

```python
#!/usr/bin/env python
import os
import requests
from dotenv import load_dotenv

load_dotenv()

NYLAS_API_KEY = os.environ.get("NYLAS_API_KEY")
SNS_TOPIC_ARN = "arn:aws:sns:us-east-2:************:didero-dev-nylas-webhooks"

# Construct SNS endpoint
sns_endpoint = f"https://sns.us-east-2.amazonaws.com/?Action=Publish&TopicArn={SNS_TOPIC_ARN}"

headers = {
    "Authorization": f"Bearer {NYLAS_API_KEY}",
    "Content-Type": "application/json"
}

# First, list existing webhooks
response = requests.get(
    "https://api.us.nylas.com/v3/webhooks",
    headers=headers
)

print("Existing webhooks:")
for webhook in response.json().get("data", []):
    print(f"- {webhook['id']}: {webhook['webhook_url']}")

# Create new webhook
webhook_data = {
    "webhook_url": sns_endpoint,
    "trigger_types": ["message.created", "grant.expired"],
    "description": f"Dev webhook for {os.environ.get('DIDERO_DEVELOPER_NAME')}",
    "notification_email_addresses": [os.environ.get("USER_EMAIL", "")]
}

response = requests.post(
    "https://api.us.nylas.com/v3/webhooks",
    json=webhook_data,
    headers=headers
)

if response.status_code == 201:
    webhook = response.json()
    print(f"\nWebhook created successfully!")
    print(f"ID: {webhook['id']}")
    print(f"Secret: {webhook['webhook_secret']}")  # Save this!
else:
    print(f"\nError: {response.status_code}")
    print(response.text)
```

## Step 2: Subscribe SQS Queue to SNS Topic

```bash
# Set your developer name
export DEVELOPER_NAME=$DIDERO_DEVELOPER_NAME

# Create subscription
aws sns subscribe \
  --topic-arn arn:aws:sns:us-east-2:************:didero-dev-nylas-webhooks \
  --protocol sqs \
  --notification-endpoint arn:aws:sqs:us-east-2:************:didero-dev-${DEVELOPER_NAME}-nylas_email_notifications \
  --profile didero-dev \
  --region us-east-2

# Update SQS queue policy to allow SNS
cat > /tmp/sqs-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "sns.amazonaws.com"
      },
      "Action": "SQS:SendMessage",
      "Resource": "arn:aws:sqs:us-east-2:************:didero-dev-${DEVELOPER_NAME}-nylas_email_notifications",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "arn:aws:sns:us-east-2:************:didero-dev-nylas-webhooks"
        }
      }
    }
  ]
}
EOF

# Apply the policy
aws sqs set-queue-attributes \
  --queue-url https://sqs.us-east-2.amazonaws.com/************/didero-dev-${DEVELOPER_NAME}-nylas_email_notifications \
  --attributes Policy=file:///tmp/sqs-policy.json \
  --profile didero-dev \
  --region us-east-2
```

## Step 3: Test Webhook Delivery

### Method 1: Send a Test Email

1. Send an email to/from an account connected to your Nylas grant
2. Monitor the queue for messages:

```bash
# Watch queue for messages
watch -n 2 "aws sqs get-queue-attributes \
  --queue-url https://sqs.us-east-2.amazonaws.com/************/didero-dev-${DEVELOPER_NAME}-nylas_email_notifications \
  --attribute-names ApproximateNumberOfMessages \
  --profile didero-dev \
  --region us-east-2"
```

### Method 2: Use Nylas Mock Payload

```python
# test_mock_webhook.py
import requests
import os
from dotenv import load_dotenv

load_dotenv()

NYLAS_API_KEY = os.environ.get("NYLAS_API_KEY")
WEBHOOK_ID = "your-webhook-id"  # From step 1

headers = {
    "Authorization": f"Bearer {NYLAS_API_KEY}",
    "Content-Type": "application/json"
}

# Get mock payload
response = requests.post(
    f"https://api.us.nylas.com/v3/webhooks/{WEBHOOK_ID}/mock-payload",
    json={"trigger_type": "message.created"},
    headers=headers
)

print("Mock payload sent!")
print(response.json())
```

## Step 4: Process Messages Locally

### Option A: Run Celery Worker

```bash
# Make sure Redis is running locally
redis-cli ping

# Run Celery worker with the nylas queue
make celery
```

### Option B: Manual Processing

```python
# test_process_message.py
import json
import boto3
from dotenv import load_dotenv
from didero.emails.tasks.nylas_notifications import process_nylas_email_notification

load_dotenv()

# Get message from queue
sqs = boto3.client('sqs', region_name='us-east-2')
developer_name = os.environ.get('DIDERO_DEVELOPER_NAME')
queue_url = f"https://sqs.us-east-2.amazonaws.com/************/didero-dev-{developer_name}-nylas_email_notifications"

response = sqs.receive_message(
    QueueUrl=queue_url,
    MaxNumberOfMessages=1,
    WaitTimeSeconds=20
)

if 'Messages' in response:
    for message in response['Messages']:
        # Parse the message
        body = json.loads(message['Body'])
        
        # Process it
        print("Processing message...")
        process_nylas_email_notification(body)
        
        # Delete from queue
        sqs.delete_message(
            QueueUrl=queue_url,
            ReceiptHandle=message['ReceiptHandle']
        )
        print("Message processed and deleted")
else:
    print("No messages in queue")
```

## Step 5: Debug Common Issues

### Check SNS Topic Logs

```bash
# Check CloudWatch logs for SNS delivery
aws logs tail /aws/sns/us-east-2/************/didero-dev-nylas-webhooks \
  --follow \
  --profile didero-dev \
  --region us-east-2
```

### Verify Webhook is Active

```python
# check_webhook_status.py
import requests
import os
from dotenv import load_dotenv

load_dotenv()

headers = {
    "Authorization": f"Bearer {os.environ.get('NYLAS_API_KEY')}",
}

response = requests.get(
    "https://api.us.nylas.com/v3/webhooks",
    headers=headers
)

for webhook in response.json().get("data", []):
    print(f"Webhook {webhook['id']}:")
    print(f"  URL: {webhook['webhook_url']}")
    print(f"  Status: {webhook.get('status', 'active')}")
    print(f"  Triggers: {webhook['trigger_types']}")
```

### Test Direct SNS Publishing

```bash
# Test publishing directly to SNS
aws sns publish \
  --topic-arn arn:aws:sns:us-east-2:************:didero-dev-nylas-webhooks \
  --message '{"type":"test","data":{"object":{"test":"message"}}}' \
  --profile didero-dev \
  --region us-east-2
```

## Cleanup

When done testing:

```bash
# Unsubscribe SQS from SNS
aws sns list-subscriptions-by-topic \
  --topic-arn arn:aws:sns:us-east-2:************:didero-dev-nylas-webhooks \
  --profile didero-dev \
  --region us-east-2

# Find your subscription ARN and unsubscribe
aws sns unsubscribe \
  --subscription-arn "arn:aws:sns:..." \
  --profile didero-dev \
  --region us-east-2
```

## Tips

1. **Use ngrok alternative**: Nylas blocks ngrok. Use VSCode port forwarding or similar
2. **Check grant status**: Ensure your Nylas grant is active before testing
3. **Monitor DLQ**: Check the dead letter queue for failed messages
4. **Use filters**: Consider SNS message filtering to route only your dev messages