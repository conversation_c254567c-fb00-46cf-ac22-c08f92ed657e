# Nylas Webhook to AWS SQS Setup Guide

## Overview

This guide explains how Nylas webhooks are processed through AWS Lambda and SQS for email processing in the Didero platform. The architecture provides developer-specific isolation and supports large messages.

## Architecture

```
<PERSON><PERSON><PERSON> Webhook → Lambda Function URL → SQS Queue → Celery Worker → Django
                        ↓
                 S3 (for large messages >256KB)
```

## Components

### Lambda Function
- **Name Pattern**: 
  - Dev: `didero-dev-{developer}-nylas-webhook`
  - Staging: `didero-staging-nylas-webhook` 
  - Prod: `didero-prod-nylas-webhook`
- **IAM Role**: Uses shared `didero-shared-nylas-lambda-role-{env}` role
- **Purpose**: Receives Nylas webhooks and formats them for Celery processing
- **Features**:
  - Validates webhook signatures (optional)
  - Converts Nylas format to Celery message format
  - Handles large messages via SQS Extended Client (S3)
  - Developer-specific isolation in dev environment

### SQS Queue
- **Name Pattern**: 
  - Dev: `didero-dev-{developer}-nylas_email_notifications`
  - Staging: `didero-staging-nylas_email_notifications`
  - Prod: `didero-prod-nylas_email_notifications`
- **Purpose**: Buffer between Lambda and Celery workers
- **Configuration**:
  - Message retention: 14 days
  - Visibility timeout: 60 seconds
  - Supports extended payloads via S3

### S3 Bucket
- **Name Pattern**:
  - Dev: `didero-dev-sqs-extended` (shared by all developers)
  - Staging: `didero-staging-sqs-extended`
  - Prod: `didero-prod-sqs-extended`
- **Purpose**: Stores message payloads >256KB

## IAM Configuration

### Shared Lambda Role

The system uses shared IAM roles for Lambda functions:
- `didero-shared-nylas-lambda-role` (dev)
- `didero-shared-nylas-lambda-role-staging` (staging)  
- `didero-shared-nylas-lambda-role-prod` (prod)

These roles have permissions for:
- SQS operations (send messages)
- S3 operations (for SQS Extended Client)
- CloudWatch logging

### Developer Permissions

For PowerUser developers, a custom policy `DideroNylasLambdaDeveloperPermissions` provides:
- `iam:GetRole` on shared roles
- `iam:PassRole` for Lambda deployment

## Deployment

### Prerequisites

1. AWS CLI configured with appropriate profile (`didero-dev`, `didero-staging`, `didero-prod`)
2. Python 3.11+ installed
3. `jq` command-line tool installed
4. For dev: `DIDERO_DEVELOPER_NAME` environment variable set in `.env`
5. For webhook creation: `NYLAS_API_KEY` environment variable (loaded from .env or pre-loaded)

### Deploy Resources

From the `nylas-webhook-lambda` directory:

```bash
# Deploy Lambda only
./deploy.sh

# Deploy and create Nylas webhook automatically
./deploy.sh --create-webhook
```

**Environment Examples:**
```bash
# Development (requires DIDERO_DEVELOPER_NAME in .env)
./deploy.sh

# Staging (uses pre-loaded environment variables)
ENVIRONMENT=staging ./deploy.sh --create-webhook

# Production (uses pre-loaded environment variables)
ENVIRONMENT=prod ./deploy.sh --create-webhook
```

The script will:
1. Create or update Lambda function using shared IAM role
2. Create SQS queue if it doesn't exist
3. Configure S3 access for large messages
4. Output the Lambda function URL
5. Optionally create Nylas webhook via API (with `--create-webhook`)

## Lambda Function Details

### Environment Variables

- `DIDERO_DEVELOPER_NAME`: Developer identifier for queue naming (dev only)
- `ENVIRONMENT`: Environment name ("dev", "staging", "prod")
- `SQS_EXTENDED_S3_BUCKET`: S3 bucket for large messages
- `NYLAS_WEBHOOK_SECRET`: (Optional) Secret for webhook signature verification

### Message Processing

The Lambda function transforms Nylas webhooks into Celery-compatible messages:

```python
# Input: Nylas webhook
{
    "type": "message.created",
    "data": {
        "object": {
            "id": "message-id",
            "grant_id": "grant-id",
            "from": [...],
            "to": [...],
            "subject": "...",
            "body": "..."
        }
    }
}

# Output: Celery message
{
    "body": base64_encoded([[webhook_body], {}, embed]),
    "headers": {
        "task": "didero.emails.tasks.nylas_notifications.process_nylas_notification",
        "id": "uuid"
    },
    "properties": {
        "body_encoding": "base64",
        "delivery_info": {
            "exchange": "",
            "routing_key": "nylas_email_notifications"
        }
    }
}
```

### Supported Webhook Types

- `message.created`: New email received
- `grant.expired`: Email credential expired
- Other Nylas events are logged but not processed

## Celery Worker Configuration

### Running the Worker

```bash
# Using make command
AWS_PROFILE=didero-dev make celery-nylas

# Or directly
AWS_PROFILE=didero-dev DJANGO_SETTINGS_MODULE=didero.settings.common \
  uv run celery -A didero worker -Q nylas_email_notifications --loglevel=info
```

### Task Processing

The `process_nylas_notification` task:
1. Checks if v2 consumer is enabled via RuntimeConfig
2. Parses the Nylas webhook payload
3. Identifies matching suppliers based on email domains/contacts
4. Creates Communication records
5. Triggers AI categorization and workflows

## Configuration

### RuntimeConfig Setting

Control which email consumer is active:

```python
# Django Admin → Runtime Configs
Key: USE_EMAIL_CONSUMER_V2
Value: true  # Enable v2 (SQS/Celery)
Value: false # Use v1 (legacy consumer)
```

### Nylas Webhook Configuration

Configure webhooks to point to your Lambda function URL:

1. **Via Nylas Dashboard**:
   - Navigate to Webhooks
   - Add webhook URL from deploy script output
   - Select events: `message.created`, `grant.expired`

2. **Via API**:
```python
import requests

webhook_data = {
    "webhook_url": "https://xxxxx.lambda-url.us-east-2.on.aws/",
    "trigger_types": ["message.created", "grant.expired"],
    "description": "Forward to Lambda"
}

response = requests.post(
    "https://api.us.nylas.com/v3/webhooks",
    json=webhook_data,
    headers={"Authorization": f"Bearer {NYLAS_API_KEY}"}
)
```

## Testing

### Test Lambda Function

```bash
# Send test message
aws lambda invoke \
  --function-name didero-dev-{developer}-nylas-webhook \
  --invocation-type RequestResponse \
  --payload '{"httpMethod":"POST","body":"{\"type\":\"test\",\"data\":{}}","headers":{}}' \
  --profile didero-dev \
  response.json

# Check response
cat response.json
```

### Monitor Logs

```bash
# Lambda logs
aws logs tail /aws/lambda/didero-dev-{developer}-nylas-webhook \
  --profile didero-dev \
  --follow

# Check queue depth
aws sqs get-queue-attributes \
  --queue-url https://sqs.us-east-2.amazonaws.com/{account}/didero-dev-{developer}-nylas_email_notifications \
  --attribute-names ApproximateNumberOfMessages \
  --profile didero-dev
```

## Troubleshooting

### Common Issues

1. **Deployment Issues**
   - **"Cannot perform operation on protected role"**: SSO roles can't be modified directly
   - **"GetRole permission denied"**: Ensure `DideroNylasLambdaDeveloperPermissions` policy is attached
   - **"Role does not exist"**: Admin needs to create shared IAM role first

2. **Lambda not receiving webhooks**
   - Verify webhook URL in Nylas dashboard
   - Check Lambda function exists and is accessible
   - Review CloudWatch logs for Lambda errors
   - Ensure Lambda function URL has public access permissions

3. **Messages not processing**
   - Verify RuntimeConfig `USE_EMAIL_CONSUMER_V2` is `true`
   - Ensure Celery worker is running with correct queue
   - Check SQS queue for messages
   - Verify Celery message format is correct (base64 encoded body)

4. **Large messages failing**
   - Verify S3 bucket exists and Lambda has permissions
   - Check SQS Extended Client configuration
   - Review Lambda logs for S3 errors

5. **Environment Variable Issues**
   - **Dev**: Ensure `DIDERO_DEVELOPER_NAME` is in `.env` file
   - **Staging/Prod**: Ensure environment variables are pre-loaded in ECS
   - **Webhook creation**: Verify `NYLAS_API_KEY` is available

6. **Signature verification failures**
   - Ensure `NYLAS_WEBHOOK_SECRET` matches Nylas configuration
   - Check webhook payload hasn't been modified

## Cleanup

To remove resources:

### Developer-Specific Cleanup (Dev Environment)

```bash
# Delete Lambda function
aws lambda delete-function \
  --function-name didero-dev-{developer}-nylas-webhook \
  --profile didero-dev

# Delete SQS queue
aws sqs delete-queue \
  --queue-url https://sqs.us-east-2.amazonaws.com/{account}/didero-dev-{developer}-nylas_email_notifications \
  --profile didero-dev

# Note: Shared IAM role and S3 bucket are NOT deleted (used by other developers)
```

### Environment Cleanup (Staging/Prod)

```bash
# Delete Lambda function
aws lambda delete-function \
  --function-name didero-{env}-nylas-webhook \
  --profile didero-{env}

# Delete SQS queue
aws sqs delete-queue \
  --queue-url https://sqs.us-east-2.amazonaws.com/{account}/didero-{env}-nylas_email_notifications \
  --profile didero-{env}

# Note: Shared IAM role and S3 bucket are NOT deleted
```