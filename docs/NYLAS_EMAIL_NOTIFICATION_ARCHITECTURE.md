# Nylas Email Notification Architecture

## System Flow Diagram

```mermaid
flowchart TB
    subgraph "Nylas"
        NE[Nylas Email Events]
    end
    
    subgraph "AWS Infrastructure"
        LF[Lambda Function URL<br/>nylas-webhook-proxy]
        SQS[SQS Queue<br/>nylas_email_notifications]
        S3[S3 Bucket<br/>didero-dev-sqs-extended]
    end
    
    subgraph "Didero API"
        CW[Celery Workers]
        ET[Email Tasks<br/>process_nylas_notification]
        DB[(Database)]
    end
    
    NE -->|Webhook POST| LF
    LF -->|Verify Signature| LF
    LF -->|Small Message < 256KB| SQS
    LF -->|Large Message > 256KB| S3
    S3 -->|S3 Reference| SQS
    SQS -->|Poll Messages| CW
    CW -->|Process| ET
    ET -->|Update| DB
    
    style NE fill:#e1f5ff
    style LF fill:#fff4e6
    style SQS fill:#fff4e6
    style S3 fill:#fff4e6
    style CW fill:#e8f5e9
    style ET fill:#e8f5e9
    style DB fill:#f3e5f5
```

## Core Design Principles

### 1. Event-Driven Email Processing
The system moves from polling or synchronous processing to an event-driven architecture where <PERSON><PERSON>s webhooks trigger immediate processing of email events. This reduces latency and improves responsiveness to email interactions.

### 2. Decoupled Processing via Message Queues
By routing Nylas webhooks through AWS Lambda → SQS → Celery, the system achieves:
- **Reliability**: Messages are persisted in SQS, preventing loss during system failures
- **Scalability**: Worker processes can be scaled independently based on queue depth
- **Resilience**: Failed processing can be retried without losing the original webhook

### 3. Type-Safe Data Contracts
The extensive TypedDict and Pydantic model definitions establish clear contracts between:
- External systems (Nylas webhooks)
- Infrastructure layers (AWS Lambda/SQS)
- Application logic (email processing)

This approach ensures data integrity and catches integration issues early through type checking.

### 4. Secure Webhook Processing
The Lambda function implements:
- **HMAC-SHA256 signature verification** to ensure webhooks originate from Nylas
- **Challenge-response handling** for initial webhook setup
- **Public endpoint with cryptographic security** rather than IP whitelisting

### 5. Large Message Handling
The system uses SQS Extended Client to handle Nylas webhooks that may exceed SQS's 256KB limit:
- **Automatic S3 storage** for messages over 256KB
- **Transparent handling** - Celery workers receive messages regardless of size
- **S3 lifecycle policies** to clean up stored messages after 7 days

### 6. Separation of Concerns
The implementation separates:
- **Queue Infrastructure**: Dedicated queue for email notifications prevents interference with other async tasks
- **Data Models**: Clear distinction between transport formats (TypedDict) and business logic models (Pydantic)
- **Task Processing**: Isolated task module for Nylas-specific logic

## Infrastructure Components

### AWS Lambda Function
- **Name**: `didero-dev-nylas-webhook-proxy` (dev environment)
- **Runtime**: Python 3.11
- **Endpoint**: Lambda Function URL (no API Gateway needed)
- **Purpose**: Receive webhooks, verify signatures, forward to SQS
- **Cost**: ~$0.20 per million requests + minimal compute time

### SQS Queue
- **Name**: `didero-dev-{developer}-nylas_email_notifications`
- **Extended Client**: Handles messages up to 2GB via S3
- **Retention**: Standard 4-day retention
- **Integration**: Polled by Celery workers

### S3 Bucket
- **Name**: `didero-dev-sqs-extended`
- **Purpose**: Store large SQS messages (>256KB)
- **Lifecycle**: Auto-delete after 7 days
- **Access**: Lambda write, Celery read/delete

### Celery Configuration
- **Queue**: `nylas_email_notifications`
- **Workers**: Configured in `docker/worker_entrypoint.sh`
- **Tasks**: Located in `didero/emails/tasks/nylas_notifications.py`

## Architectural Benefits

### Improved Observability
With messages flowing through SQS and Celery, the system gains:
- Queue depth metrics for monitoring backlog
- Task execution metrics from Celery
- Clear failure points for debugging

### Enhanced Reliability
The message queue pattern provides:
- Automatic retries for transient failures
- Dead letter queues for persistent failures
- No message loss during deployments or restarts

### Flexible Integration
The supplier lookup data structures suggest the system will:
- Correlate incoming emails with known suppliers
- Track email interactions at the domain and contact level
- Enable business logic based on email participants

## Strategic Implications

This architecture positions the system to:
1. **React in real-time** to customer emails rather than batch processing
2. **Scale email processing** independently of other system components
3. **Build email-driven workflows** that trigger business processes
4. **Maintain audit trails** of all email interactions through the message queue

The reduction in ECS CPU allocation suggests confidence in the efficiency of this event-driven approach compared to previous implementations.

## Deployment and Configuration

### Lambda Deployment
The Lambda function is packaged and deployed using:
```bash
cd nylas-webhook-lambda
./deploy.sh
```

### Environment Variables
Lambda requires:
- `ENVIRONMENT`: dev/staging/prod
- `DEVELOPER_NAME`: Developer name for dev environment
- `SQS_EXTENDED_S3_BUCKET`: S3 bucket for large messages
- `NYLAS_WEBHOOK_SECRET`: HMAC secret for signature verification

### Queue Creation
New queues are created using:
```bash
DJANGO_SETTINGS_MODULE=didero.settings.dev uv run python manage.py runscript make_queues --script-args --env=dev
```

### Webhook Configuration
Configure Nylas to send webhooks to the Lambda Function URL with appropriate event types for email tracking.